{"private": true, "workspaces": {"packages": ["api", "web"]}, "devDependencies": {"@playwright/test": "^1.42.1", "@redwoodjs/auth-custom-setup": "8.5.0", "@redwoodjs/core": "8.5.0", "@types/node": "^20.11.30", "prettier-plugin-tailwindcss": "0.4.1"}, "eslintConfig": {"extends": "@redwoodjs/eslint-config", "root": true}, "engines": {"node": "=18.x", "yarn": ">=1.15"}, "prisma": {"seed": "yarn rw exec seed"}, "packageManager": "yarn@3.6.3", "dependencies": {"@ant-design/charts": "^1.4.2", "@heroicons/react": "^2.0.18", "@mapbox/mapbox-sdk": "^0.15.3", "@nivo/core": "^0.84.0", "@nivo/geo": "^0.84.0", "@propelauth/javascript": "^2.0.20", "@propelauth/node": "^2.1.31", "antd": "^5.10.1", "axios-retry": "^4.4.0", "babel-plugin-graphql-tag": "^3.3.0", "core-js": "^3.37.1", "country-code-lookup": "^0.1.3", "d3-geo": "^3.1.0", "d3-scale": "^4.0.2", "docx": "^8.5.0", "dotenv": "^16.4.5", "epubjs": "^0.3.93", "file-saver": "^2.0.5", "framer-motion": "^11.11.9", "he": "^1.2.0", "heroicons": "^2.0.18", "html-to-pdfmake": "^2.5.12", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "isomorphic-dompurify": "^2.11.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "mammoth": "^1.8.0", "mapbox-gl": "^3.1.2", "moment": "^2.30.1", "nodemailer": "^6.9.14", "papaparse": "^5.4.1", "playwright": "^1.42.1", "react-pdf": "^9.0.0", "react-resizable": "^3.0.5", "react-simple-maps": "^3.0.0", "react-table": "^7.8.0", "react-to-print": "^2.14.15", "reactflow": "^11.11.4", "recharts": "^2.9.1", "web": "workspace:^", "xlsx": "^0.18.5"}, "scripts": {"test:e2e": "npx playwright test tests/e2e --workers=1", "test:processModel": "npx playwright test tests/processmodel --workers=1", "test:labs": "npx playwright test tests/labs --workers=1"}}