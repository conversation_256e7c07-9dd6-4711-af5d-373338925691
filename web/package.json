{"name": "web", "version": "0.0.0", "private": true, "browserslist": {"development": ["last 1 version"], "production": ["defaults"]}, "dependencies": {"@heroicons/react": "1.0.6", "@redwoodjs/auth": "8.5.0", "@redwoodjs/forms": "8.5.0", "@redwoodjs/router": "8.5.0", "@redwoodjs/web": "8.5.0", "@sentry/browser": "8", "@sentry/react": "7", "@tremor/react": "^3.11.1", "prop-types": "15.8.1", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^4.11.0", "uuid": "^10.0.0"}, "devDependencies": {"@redwoodjs/vite": "8.5.0", "autoprefixer": "^10.4.16", "daisyui": "latest", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "tailwindcss": "^3.3.5"}}