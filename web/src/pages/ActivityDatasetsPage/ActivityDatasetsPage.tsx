import { Link, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { Layout, Menu, theme } from 'antd'
import { getOrgMemberInfo, useAuth } from 'src/auth'
const { Content } = Layout
import { Metadata } from '@redwoodjs/web'
import ActivityDatasetsCell from 'src/components/ActivityDatasetsCell/ActivityDatasetsCell'

const ActivityDatasetsPage = () => {
  const { userMetadata } = useAuth()

  return (
    <>
      <MetaTags title="Background Activity Datasets" description="Background Activity Datasets page" />

      <Content>
        <Layout>
          <Content style={{minHeight: 280 }}>
            <ActivityDatasetsCell />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default ActivityDatasetsPage
