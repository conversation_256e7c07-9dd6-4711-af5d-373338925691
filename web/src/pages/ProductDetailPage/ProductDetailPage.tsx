import { MetaTags } from '@redwoodjs/web'
import { Layout } from 'antd'
const { Content } = Layout
import './benchmark.css'
import ProductInfoCell from 'src/components/ProductInfoCell'
import { useParams } from '@redwoodjs/router'

const ProductDetailPage = () => {
  const { tenantID, productId } = useParams()

  return (
    <>
      <MetaTags title="Product Detail" description="Product Detail page" />
      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            <ProductInfoCell productId={productId} />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default ProductDetailPage
