import { Link, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { Layout, Menu, theme } from 'antd'
const { Content } = Layout
import LabEfMatchingComponent from 'src/components/LabEFMatchingComponent'

const LabEfMatchingPage = () => {
  return (
    <>
      <Metadata
        title="CarbonBright Labs | AI Emission Factor Matching"
        description="Match your product raw materials emissions factors with our advanced AI Emission Factor Matching Tool"
        ogUrl="https://app.carbonbright.co/labs/ef-matching"
        ogTitle="CarbonBright Labs | AI Emission Factor Matching"
        ogDescription="Match your product raw materials emissions factors with our advanced AI Emission Factor Matching Tool"
        // Add other relevant meta tags
      />
      <Content>
        <Layout>
          <Content style={{minHeight: 280 }}>
            <LabEfMatchingComponent />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default LabEfMatchingPage