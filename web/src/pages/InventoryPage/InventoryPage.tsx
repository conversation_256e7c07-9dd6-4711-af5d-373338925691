import { Link, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { Layout, Menu, theme } from 'antd'
const { Content } = Layout
import ProductsCell from 'src/components/ProductsCell'

const InventoryPage = () => {
  return (
    <>
      <MetaTags title="Inventory" description="Inventory page" />

      <Content>
        <Layout>
          <Content style={{minHeight: 280 }}>
            <ProductsCell />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default InventoryPage
