import { MetaTags } from '@redwoodjs/web'
import { Layout, Tabs } from 'antd'
import type { TabsProps } from 'antd'
import { useState } from 'react'
const { Content } = Layout
import DashboardCell from 'src/components/DashboardCell'
import GettingStartedCell from 'src/components/GettingStartedCell'

const HomePage = () => {

  return (
    <>
      <MetaTags
        title="Welcome | CarbonBright"
        description="Welcome"
      />
      <Content>
        <Layout>
          <Content>
            <GettingStartedCell />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default HomePage
