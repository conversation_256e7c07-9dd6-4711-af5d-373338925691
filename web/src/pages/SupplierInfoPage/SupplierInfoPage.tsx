import { MetaTags } from '@redwoodjs/web'
import { Layout } from 'antd'
const { Content } = Layout
import SupplierInfoCell from 'src/components/SupplierInfoCell'
import { useParams } from '@redwoodjs/router'

const SupplierInfoPage = () => {
  const { supplierId } = useParams()

  return (
    <>
      <MetaTags title="Supplier Detail" description="Supplier Detail page" />
      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            <SupplierInfoCell supplierId={supplierId} />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default SupplierInfoPage
