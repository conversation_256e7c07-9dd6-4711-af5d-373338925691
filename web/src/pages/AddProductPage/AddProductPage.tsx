import React, { useState, useEffect } from 'react'
import { MetaTags } from '@redwoodjs/web'
import { Layout } from 'antd'
const { Content } = Layout
import { getOrgMemberInfo, useAuth } from 'src/auth'
import AddProduct from 'src/components/AddProduct'
import { useLazyQuery } from '@apollo/client'
import LoadingSkeleton from 'src/components/LoadingSkeleton/LoadingSkeleton'

export const PRODUCTS_QUERY = gql`
  query ProductsQuery {
    getProducts {
      productName
      productId
      clonedFromProductId
    }
  }
`

const AddProductPage = () => {
  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  const [isAddProductFeatureEnabled, setIsAddProductFeatureEnabled] = useState(true)

  const [getProducts, { loading, data: products }] =
    useLazyQuery(PRODUCTS_QUERY)

  useEffect(() => {
    if (userMetadata.user.metadata?.isTrialUser) {
      getProducts()
    }
  }, [userMetadata, getProducts])

  useEffect(() => {
    if (products) {
      const productCount = products.getProducts.filter(
        (x) => x.clonedFromProductId === null
      ).length

      if (productCount >= (orgMemberInfo?.orgMetadata?.addProductCount ?? 0)) {
        setIsAddProductFeatureEnabled(false)
      }
    }
  }, [products, orgMemberInfo, userMetadata])

  if (loading) return <LoadingSkeleton />

  return (
    <>
      <MetaTags title="Add Product" description="New Product page" />
      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            {isAddProductFeatureEnabled ? <AddProduct /> : 'Not Found'}
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default AddProductPage
