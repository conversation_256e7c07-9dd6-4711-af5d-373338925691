import { MetaTags } from '@redwoodjs/web'
import { Layout } from 'antd'
const { Content } = Layout
import './benchmark.css'
import ProductInfoCell from 'src/components/ProductInfoCell'
import { useParams } from '@redwoodjs/router'

const ComponentDetailPage = () => {
  const { tenantID, productId } = useParams()

  return (
    <>
      <MetaTags title="Component Detail" description="Component Detail page" />
      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            <ProductInfoCell productId={productId} component={true} />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default ComponentDetailPage
