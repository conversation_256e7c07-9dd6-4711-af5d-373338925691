import { MetaTags } from '@redwoodjs/web'
import { Layout } from 'antd'
const { Content } = Layout
import { getOrgMemberInfo, useAuth } from 'src/auth'
import AddProduct from 'src/components/AddProduct'
import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import LoadingSkeleton from 'src/components/LoadingSkeleton/LoadingSkeleton'

export const PRODUCTS_QUERY = gql`
  query ProductsQuery {
    getProducts {
      productName
      productId
      clonedFromProductId
    }
  }
`

const AddComponentImportFilePage = () => {
  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const [isAddComponentFeatureEnabled, setIsAddComponentFeatureEnabled] =
    useState(orgMemberInfo?.orgMetadata?.addComponentEnabled ?? false)

  const [getProducts, { loading, data: products }] =
    useLazyQuery(PRODUCTS_QUERY)

  useEffect(() => {
    if (userMetadata.user.metadata?.isTrialUser) {
      getProducts()
    }
  }, [userMetadata, getProducts])

  if (loading) return <LoadingSkeleton />

  return (
    <>
      <MetaTags title="Import Component" description="New Component page" />
      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            {isAddComponentFeatureEnabled ? (
              <AddProduct importFile={true} component={true} />
            ) : (
              'Not Found'
            )}
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default AddComponentImportFilePage
