import { Link, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { Layout, Menu, theme } from 'antd'
import { getOrgMemberInfo, useAuth } from 'src/auth'
const { Content } = Layout
import ComponentCell from 'src/components/ComponentCell/ComponentCell'

const ComponentPage = () => {
  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)


  return (
    <>
      <MetaTags title="Component" description="Component page" />

      <Content>
        <Layout>
          <Content style={{minHeight: 280 }}>
          {orgMemberInfo?.orgMetadata?.addComponentEnabled ? (
               <ComponentCell />
            ) : (
              'Not Found'
            )}
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default ComponentPage
