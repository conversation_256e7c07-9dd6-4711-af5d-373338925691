import { createAuthentication } from '@redwoodjs/auth'
import {
  createClient,
  IAuthClient,
  OrgMemberInfo,
} from '@propelauth/javascript'
import moment from 'moment'

// If you're integrating with an auth service provider you should delete this interface.
// Instead you should import the type from their auth client sdk.

// If you're integrating with an auth service provider you should delete this interface.
// This type should be inferred from the general interface above.
interface User {
  // The name of the id variable will vary depending on what auth service
  // provider you're integrating with. Another common name is `sub`
  id: string
  email?: string
  username?: string
  roles: string[]
}

// If you're integrating with an auth service provider you should delete this interface
// This type should be inferred from the general interface above
export interface ValidateResetTokenResponse {
  error?: string
  [key: string]: string | undefined
}

// Replace this with the auth service provider client sdk
const client = createClient({
  authUrl: process.env.PROPELAUTH_AUTH_URL || 'http://localhost',

  // If true, periodically refresh the access token in the background.
  // This helps ensure you always have a valid token ready to go. Default true.
  enableBackgroundTokenRefresh: true,
})

function createAuth() {
  const authImplementation = createAuthImplementation(client)

  // You can pass custom provider hooks here if you need to as a second
  // argument. See the Redwood framework source code for how that's used
  return createAuthentication(authImplementation)
}

// This is where most of the integration work will take place. You should keep
// the shape of this object (i.e. keep all the key names) but change all the
// values/functions to use methods from the auth service provider client sdk
// you're integrating with
function createAuthImplementation(client: IAuthClient) {
  return {
    type: 'custom-auth',
    client,
    login: async () => client.redirectToLoginPage(),
    logout: async () => client.logout(true),
    signup: async () => client.redirectToSignupPage(),
    getToken: async () =>
      (await client.getAuthenticationInfoOrNull())?.accessToken,
    getUserMetadata: async () => client.getAuthenticationInfoOrNull(),

    /**
     * Actual user metadata might look something like this
     * {
     *   "id": "11111111-2222-3333-4444-5555555555555",
     *   "aud": "authenticated",
     *   "role": "authenticated",
     *   "roles": ["admin"],
     *   "email": "<EMAIL>",
     *   "app_metadata": {
     *     "provider": "email"
     *   },
     *   "user_metadata": null,
     *   "created_at": "2016-05-15T19:53:12.368652374-07:00",
     *   "updated_at": "2016-05-15T19:53:12.368652374-07:00"
     * }
     */
  }
}

function addDaysToTimestamp(timestamp, days): number {
  const date = new Date(timestamp)
  date.setDate(date.getDate() + days)
  return date.getTime()
}

function getDaysDifference(timestamp1: number, timestamp2: number): number {
  const date1 = new Date(timestamp1)
  const date2 = new Date(timestamp2)

  if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
    throw new Error('Invalid date format')
  }

  const differenceInMilliseconds = date2.getTime() - date1.getTime()
  const millisecondsPerDay = 1000 * 60 * 60 * 24
  const differenceInDays = Math.round(
    differenceInMilliseconds / millisecondsPerDay
  )
  return differenceInDays
}

export const getUserTrialInfo = function (userMetaData) {
  const trialInfo = {
    isTrialUser: false,
    trialPeriodDays: null,
    trialEndDate: null,
    daysRemaining: null,
    hasTrialExpired: false,
  }

  if (!userMetaData) {
    return trialInfo
  }

  if (userMetaData.user.metadata?.isTrialUser) {
    trialInfo.isTrialUser = true
    trialInfo.trialPeriodDays = userMetaData.user.metadata?.trialPeriodDays ?? 0
    trialInfo.trialEndDate = addDaysToTimestamp(
      userMetaData.user.createdAt * 1000,
      trialInfo.trialPeriodDays
    )
    trialInfo.daysRemaining = getDaysDifference(
      new Date().getTime(),
      trialInfo.trialEndDate
    )
    if (trialInfo.daysRemaining < 0) {
      trialInfo.hasTrialExpired = true
    }
  }

  return trialInfo
}

export const getOrgMemberInfo = function (userMetaData) {
  if (!userMetaData) {
    return null
  }

  const orgs = userMetaData.orgIdToOrgMemberInfo
  const ordId = Object.keys(orgs)[0] // For now, we only support one org per user
  const orgMemberInfo = orgs[ordId] as OrgMemberInfo

  return orgMemberInfo
}

export const { AuthProvider, useAuth } = createAuth()
