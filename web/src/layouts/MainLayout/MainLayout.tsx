import 'core-js/full/promise/with-resolvers'
import { useAuth, getUserTrialInfo, getOrgMemberInfo } from 'src/auth'
import { Link, navigate, routes, useLocation } from '@redwoodjs/router'
import Logo from 'src/components/Logo/Logo'
import { LogoutOutlined, LoginOutlined, BarsOutlined } from '@ant-design/icons'
import { useEffect, useState } from 'react'
import {
  HomeOutlined,
  DropboxOutlined,
  FileExcelOutlined,
  ApartmentOutlined,
  UserOutlined,
  ClockCircleOutlined,
  AppstoreAddOutlined,
  DashboardOutlined,
  DotChartOutlined,
} from '@ant-design/icons'
import {
  Layout,
  Menu,
  Button,
  ConfigProvider,
  Tag,
  Avatar,
  Dropdown,
  MenuProps,
  Tooltip,
  Divider,
  Alert,
} from 'antd'
import './style.css'
import { useServiceHubChatBot } from 'src/hooks/useServiceHubChatBot'
import UpgradePlan from 'src/components/UpgradePlan/UpgradePlan'
import Sentry from 'src/lib/sentry'
import HelpAndSupport from 'src/components/HelpAndSupport/HelpAndSupport'
import LoadingSkeleton from 'src/components/LoadingSkeleton/LoadingSkeleton'
import ImpactFactorSelector from 'src/components/ImpactFactorSelector/ImpactFactorSelector'

const { Header, Content, Footer, Sider } = Layout

type MainLayoutProps = {
  children?: React.ReactNode
}

const MainLayout = ({ children }: MainLayoutProps) => {
  const { isAuthenticated, userMetadata, logIn, logOut } = useAuth()
  const location = useLocation()
  const [upgradePlanModalIsVisible, setUpgradePlanModalIsVisible] =
    useState(false)

  const [isMobile, setIsMobile] = useState(false)
  const [hasOrgs, setHasOrgs] = useState(false)

  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.matchMedia('(max-width: 1024px)').matches)
    }

    checkIfMobile()

    window.addEventListener('resize', checkIfMobile)

    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  useEffect(() => {
    if (userMetadata && userMetadata.user) {
      Sentry.setUser(userMetadata.user)
    }
    const checkUserMetadata = () => {
      if (isAuthenticated && userMetadata) {
        const orgs = userMetadata.orgIdToOrgMemberInfo || {}
        const userHasOrgs = Object.keys(orgs).length > 0
        setHasOrgs(userHasOrgs)

        if (!userHasOrgs) {
          navigate(routes.labHome())
        }
      }
    }

    checkUserMetadata()
  }, [userMetadata, isAuthenticated])

  const getSelectedMenuKeys = () => {
    const pathSegments = location.pathname.split('/')
    if (location.pathname === '/') {
      return ['home']
    } else if (
      pathSegments.includes('product-detail') ||
      pathSegments.includes('product')
    ) {
      return ['inventory']
    } else if(pathSegments.includes('component-detail')) {
      return ['component']
    } else {
      return [pathSegments[1]]
    }
  }

  const getPageTitle = () => {
    const pathSegments = location.pathname.split('/')
    switch (location.pathname) {
      case '/':
        return 'Welcome!'
      case '/dashboard':
        return 'Product Emissions Dashboard'
      case '/products':
        return 'Products'
      case '/component':
        return 'Components'
      case '/product/add':
        return 'Add Product'
      case '/product/import':
        return 'Import Product'
      case '/component/import':
        return 'Import Component'
      case '/reports':
        return 'Reports'
      case '/suppliers':
        return 'Suppliers'
      case '/datasets':
        return 'Datasets'
      case '/reports/inventory-summary':
        return 'Inventory Summary'
      case '/reports/emissions-by-category':
        return 'Category Emissions Report'
      case '/reports/emissions-by-supplier':
        return 'Supplier Emissions Report'
      case '/recommendations':
        return 'Recommendations'
      default:
        if (pathSegments.includes('product-detail')) {
          return 'Product Detail'
        } else if(pathSegments.includes('component-detail')) {
          return 'Component Detail'
        } else if (pathSegments.includes('compare-product')) {
          return 'Scenario Modeling'
        } else if (pathSegments.includes('supplier-info')) {
          return 'Supplier Detail'
        } else if (pathSegments[1] == 'product' && pathSegments[3] == 'edit') {
          return 'Edit Product'
        }
        return ''
    }
  }

  const userTrialInfo = getUserTrialInfo(userMetadata)

  const trialCountdown = () => {
    if (userTrialInfo.isTrialUser) {
      if (!userTrialInfo.hasTrialExpired) {
        const tooltipText = `Your free trial ends in ${userTrialInfo.daysRemaining} days. Click to upgrade your plan`
        return (
          <>
            <Tooltip title={tooltipText}>
              <Tag
                style={{ fontSize: 14, fontWeight: 600, cursor: 'pointer' }}
                icon={<ClockCircleOutlined />}
                color="warning"
                onClick={() => setUpgradePlanModalIsVisible(true)}
              >
                {userTrialInfo.daysRemaining} days left
              </Tag>
            </Tooltip>
            <Divider type="vertical" />
          </>
        )
      } else {
        return (
          <>
            <Tooltip title="Your free trial has ended. Click to upgrade your plan">
              <Tag icon={<ClockCircleOutlined />} color="error">
                Trial Ended
              </Tag>
            </Tooltip>
            <Divider type="vertical" />
          </>
        )
      }
    }
  }

  const profileItems: MenuProps['items'] = [
    {
      label: (
        <Button
          style={{ color: 'black' }}
          type="link"
          id="add-single-product-button"
          onClick={() =>
            (window.location.href = `${process.env.PROPELAUTH_AUTH_URL}/account`)
          }
          icon={<UserOutlined />}
        >
          Profile
        </Button>
      ),
      key: '1',
    },
    {
      label: (
        <Button
          style={{ color: 'black' }}
          type="link"
          icon={<LogoutOutlined />}
          onClick={logOut}
        >
          Logout
        </Button>
      ),
      key: '2',
    },
  ]

  if (!hasOrgs) {
    ;<LoadingSkeleton />
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          fontFamily: 'Montserrat, sans-serif',
        },
      }}
    >
      {isMobile ? (
        <Alert
          message={
            <>
              <p style={{ fontWeight: 'bold' }}>
                Screen resolution not supported
              </p>
            </>
          }
          description={
            <>
              <p>
                Your screen resolution is too low to display this content
                properly. Please do one of the following to resolve this issue:
              </p>
              <ul>
                <li>
                  &#8226; Resize your browser window to a width of at least 1024
                  pixels.
                </li>
                <li>
                  &#8226; Use a device with a screen width of at least 1024
                  pixels.
                </li>
              </ul>
            </>
          }
          type="warning"
          showIcon
        />
      ) : (
        <Layout style={{ minHeight: '100vh' }}>
          <Sider width={250} theme="light" collapsible>
            <div
              style={{ margin: '10px 10px 5px 20px', maxWidth: '75%' }}
              className="logo"
            >
              <Logo />
            </div>
            <Menu
              style={{ marginTop: '20px' }}
              theme="light"
              mode="inline"
              selectedKeys={getSelectedMenuKeys()}
            >
              <Menu.Item key="home" id="menu-item-home" icon={<HomeOutlined />}>
                <Link style={{ fontSize: '16px' }} to="/">
                  Home
                </Link>
              </Menu.Item>
              <Menu.Item key="dashboard" id="menu-item-dashboard" icon={<DashboardOutlined />}>
                <Link style={{ fontSize: '16px' }} to="/dashboard">
                  Dashboard
                </Link>
              </Menu.Item>
              <Menu.Item
                key="products"
                id="menu-item-inventory"
                icon={<DropboxOutlined />}
              >
                <Link style={{ fontSize: '16px' }} to="/products">
                  Products
                </Link>
              </Menu.Item>
              {orgMemberInfo?.orgMetadata?.addComponentEnabled && (
                <Menu.Item
                key="component"
                id="menu-item-component"
                icon={<AppstoreAddOutlined />}
              >
                <Link style={{ fontSize: '16px' }} to="/component">
                  Components
                </Link>
              </Menu.Item>
                )}
              {/* <Menu.Item key="recommendations" hidden icon={<BulbOutlined />}>
              <Link style={{ fontSize: '16px' }} to="/recommendations">
                Recommendations
              </Link>
            </Menu.Item> */}
              <Menu.Item
                key="reports"
                id="menu-item-inventory"
                icon={<FileExcelOutlined />}
              >
                <Link style={{ fontSize: '16px' }} to="/reports">
                  Reports
                  {userTrialInfo.isTrialUser && (
                    <Tag color="#FFC000" style={{ marginLeft: '10px' }}>
                      PRO
                    </Tag>
                  )}
                </Link>
              </Menu.Item>
              <Menu.Item
                key="suppliers"
                id="menu-item-suppliers"
                icon={<ApartmentOutlined />}
              >
                <Link style={{ fontSize: '16px' }} to="/suppliers">
                  Suppliers
                  {userTrialInfo.isTrialUser && (
                    <Tag color="#FFC000" style={{ marginLeft: '10px' }}>
                      PRO
                    </Tag>
                  )}
                </Link>
              </Menu.Item>
              <Divider />
              {orgMemberInfo?.orgMetadata?.activityDatasetEnabled && (
                <Menu.Item
                  key="datasets"
                  id="menu-item-datasets"
                  icon={<BarsOutlined />}
                >
                <Link style={{ fontSize: '16px' }} to="/datasets">
                  Datasets
                </Link>
              </Menu.Item>
              )}
              <Menu.Item key="labs" id="menu-item-labs" icon={<DotChartOutlined />}>
                <a style={{ fontSize: '16px' }} href="/labs" target="_blank" rel="noopener noreferrer">Labs</a>
              </Menu.Item>
            </Menu>
          </Sider>
          <Layout className="site-layout">
            <Header
              className="site-layout-background"
              style={{
                padding: 0,
                backgroundColor: 'white',
                textAlign: 'right',
              }}
            >
              <h1
                style={{
                  fontSize: '25px',
                  fontWeight: 600,
                  textAlign: 'left',
                  marginLeft: 40,
                  position: 'absolute',
                }}
              >
                {getPageTitle()}
              </h1>
              {isAuthenticated ? (
                <>
                  <UpgradePlan
                    visible={upgradePlanModalIsVisible}
                    onClose={() => setUpgradePlanModalIsVisible(false)}
                    message={`Your free trial ends in ${userTrialInfo.daysRemaining} days. Please upgrade your plan to unlock all PRO features.`}
                  />
                  {trialCountdown()}
                  <span style={{ marginRight: 16 }}>
                    <ImpactFactorSelector />
                    <HelpAndSupport userMetadata={userMetadata} />
                    <Dropdown
                      menu={{
                        items: profileItems,
                      }}
                    >
                      <Avatar
                        style={{
                          backgroundColor: '#fde3cf',
                          color: '#f56a00',
                          cursor: 'pointer',
                        }}
                      >
                        {userMetadata.user.email.charAt(0).toUpperCase()}
                      </Avatar>
                    </Dropdown>
                  </span>
                </>
              ) : (
                <Button onClick={() => logIn()} icon={<LoginOutlined />}>
                  Log In
                </Button>
              )}
            </Header>
            <Content style={{ margin: '16px' }}>
              <div
                className="site-layout-background"
                style={{ paddingLeft: 10, minHeight: 360 }}
              >
                {userTrialInfo.hasTrialExpired ? (
                  <UpgradePlan
                    visible={true}
                    onClose={false}
                    message="Your free trial has ended. Please upgrade your plan to continue using the app."
                  />
                ) : (
                  children
                )}
              </div>
            </Content>
            <Footer style={{ textAlign: 'center' }}>CarbonBright Inc.</Footer>
          </Layout>
        </Layout>
      )}
    </ConfigProvider>
  )
}

export default MainLayout
