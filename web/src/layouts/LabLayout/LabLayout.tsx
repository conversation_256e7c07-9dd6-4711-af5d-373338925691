import 'core-js/full/promise/with-resolvers'
import { useAuth, getUserTrialInfo } from 'src/auth'
import { Link, navigate, routes, useLocation } from '@redwoodjs/router'
import Logo from 'src/components/Logo/Logo'
import { LogoutOutlined, LoginOutlined } from '@ant-design/icons'
import { useEffect, useState } from 'react'
import {
  UserOutlined,
} from '@ant-design/icons'
import {
  Layout,
  Button,
  ConfigProvider,
  Avatar,
  Dropdown,
  MenuProps,
  Alert,
} from 'antd'
import { useServiceHubChatBot } from 'src/hooks/useServiceHubChatBot'
import Sentry from 'src/lib/sentry'

const { Header, Content, Footer } = Layout

type LabLayoutProps = {
  children?: React.ReactNode
}

const LabLayout = ({ children }: LabLayoutProps) => {
  const { isAuthenticated, userMetadata, logIn, logOut } = useAuth()

  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.matchMedia('(max-width: 1024px)').matches)
    }

    checkIfMobile()

    window.addEventListener('resize', checkIfMobile)

    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  useEffect(() => {
    if (userMetadata && userMetadata.user) {
      Sentry.setUser(userMetadata.user)
    }
  }, [userMetadata])


  const profileItems: MenuProps['items'] = [
    {
      label: (
        <Button
          style={{ color: 'black' }}
          type="link"
          id="add-single-product-button"
          onClick={() => window.location.href = `${process.env.PROPELAUTH_AUTH_URL}/account`}
          icon={<UserOutlined />}
        >
          Profile
        </Button>
      ),
      key: '1',
    },
    {
      label: (
        <Button
          style={{ color: 'black' }}
          type="link"
          icon={<LogoutOutlined />}
          onClick={logOut}
        >
          Logout
        </Button>
      ),
      key: '2',
    },
  ]

  return (
    <ConfigProvider
      theme={{
        token: {
          fontFamily: 'Montserrat, sans-serif',
        },
      }}
    >
      {isMobile ? (
        <Alert
          message={
            <>
              <p style={{ fontWeight: 'bold' }}>
                Screen resolution not supported
              </p>
            </>
          }
          description={
            <>
              <p>
                Your screen resolution is too low to display this content
                properly. Please do one of the following to resolve this issue:
              </p>
              <ul>
                <li>
                  &#8226; Resize your browser window to a width of at least 1024
                  pixels.
                </li>
                <li>
                  &#8226; Use a device with a screen width of at least 1024
                  pixels.
                </li>
              </ul>
            </>
          }
          type="warning"
          showIcon
        />
      ) : (
        <Layout style={{ minHeight: '100vh' }}>

          <Layout className="site-layout">
            <Header
              className="site-layout-background"
              style={{
                padding: 0,
                backgroundColor: 'white',
                textAlign: 'right',
              }}
            >
              <h1
                style={{
                  fontSize: '25px',
                  fontWeight: 600,
                  textAlign: 'left',
                  marginLeft: 40,
                  marginTop: 10,
                  position: 'absolute',
                  cursor: 'pointer',
                }}
                onClick={() => navigate(routes.labHome())}
              >
                <Logo />
              </h1>
              {isAuthenticated ? (
                <>
                  <span style={{ marginRight: 16 }}>
                    <Dropdown
                      menu={{
                        items: profileItems,
                      }}
                    >
                      <Avatar
                        style={{
                          backgroundColor: '#fde3cf',
                          color: '#f56a00',
                          cursor: 'pointer',
                        }}
                      >
                        {userMetadata.user.email.charAt(0).toUpperCase()}
                      </Avatar>
                    </Dropdown>
                  </span>
                </>
              ) : (
                <Button onClick={() => logIn()} icon={<LoginOutlined />}>
                  Log In
                </Button>
              )}
            </Header>
            <Content style={{ margin: '16px' }}>
              <div
                className="site-layout-background"
                style={{ paddingLeft: 10, minHeight: 360 }}
              >
                {(
                  children
                )}
              </div>
            </Content>
            <Footer style={{ textAlign: 'center' }}>CarbonBright Inc.</Footer>
          </Layout>
        </Layout>
      )}
    </ConfigProvider>
  )
}

export default LabLayout
