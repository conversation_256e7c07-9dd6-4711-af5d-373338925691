import { useState, useEffect } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
  Button,
  Drawer,
  Form,
  Input,
  Row,
  Col,
  Select,
  Radio,
  Tag,
  Space,
  message,
  Divider,
  Modal,
  Steps,
  Tooltip,
  Descriptions,
  InputNumber,
} from 'antd'
import { SearchOutlined, EditOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { v4 as uuidv4 } from 'uuid'
import DataTable from '../DataTable/DataTable'
import IntermediateExchanges from '../IntermediateExchanges'
import countries from 'src/components/AddProduct/countries'
import countryCodeLookup from 'country-code-lookup'
import { PREDICT_EMISSIONS_FACTORS_QUERY, SEARCH_EMISSIONS_FACTORS_QUERY, CREATE_EMISSIONS_FACTOR_MUTATION} from 'src/utils/graphql'
import { useMutation } from '@redwoodjs/web'
import { getOrgMemberInfo, useAuth } from 'src/auth'

const EmissionsFactorSelector = ({
  isOpen,
  onClose,
  selectedItem,
  onEmissionsFactorUpdate,
  initialEmissionsFactor = null,
  initialEmissionsFactorMatches = [],
  editMode = false,
  geographyModelingEnabled = false,
  productCategoryEnabled = false,
  activityType = 'material',
}) => {
  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const orgName =
    orgMemberInfo && orgMemberInfo.orgName ? orgMemberInfo.orgName : null

  const [emissionsFactorMatchForm] = Form.useForm()
  const [addCustomEmissionsFactorForm] = Form.useForm()
  const [emissionsFactorMatches, setEmissionsFactorMatches] = useState(
    initialEmissionsFactorMatches
  )
  const [emissionsFactorMatchesTableKey, setEmissionsFactorMatchesTableKey] =
    useState(uuidv4())
  const [selectedEmissionsFactor, setSelectedEmissionsFactor] = useState(
    initialEmissionsFactor
  )
  const [searchLoading, setSearchLoading] = useState(false)

  const [isIntermediateExchangesOpen, setIsIntermediateExchangesOpen] =
    useState(false)
  const [selectedActivityForExchanges, setSelectedActivityForExchanges] =
    useState(null)
  const [isAddCustomEFModalOpen, setIsAddCustomEFModalOpen] =
    useState(null)
  const [createEmissionsFactor, { loading: createEmissionsFactorIsLoading }] =
  useMutation(CREATE_EMISSIONS_FACTOR_MUTATION)


  const [predictEmissionsFactors] = useLazyQuery(
    PREDICT_EMISSIONS_FACTORS_QUERY
  )

  useEffect(() => {
    if (isOpen) {
      initializeForm()
      setEmissionsFactorMatches(initialEmissionsFactorMatches)
      setSelectedEmissionsFactor(initialEmissionsFactor)
      if (!initialEmissionsFactorMatches?.length) {
        handleEmissionsFactorSearch()
      }
      setEmissionsFactorMatchesTableKey(uuidv4())
    }
  }, [
    isOpen,
    selectedItem,
    initialEmissionsFactor,
    initialEmissionsFactorMatches,
  ])

  const initializeForm = () => {
    const geography = (() => {
      return selectedItem?.geography ?? 'GLO'
    })()

    emissionsFactorMatchForm.setFieldsValue({
      chemicalName: selectedItem?.chemicalName,
      geography,
    })
  }

  const handleEmissionsFactorSearch = async () => {
    try {
      const values = await emissionsFactorMatchForm.validateFields()
      setSearchLoading(true)

      const emissionsFactorMatch = {
        activityMatch: null,
        activityMatches: [],
      }

      const response = await predictEmissionsFactors({
        variables: {
          chemicalName: values.chemicalName,
          productCategory: productCategoryEnabled ? selectedItem.productCategory : null,
          casNo: selectedItem.casNo,
          geography: values.geography,
          geographyModeling: geographyModelingEnabled,
          unit: selectedItem.unit,
        },
      })

      if (response?.data?.predictEmissionsFactors?.recommendations?.length) {
        emissionsFactorMatch.activityMatch = {
          ...(response?.data?.predictEmissionsFactors?.matchedActivity ??
            emissionsFactorMatch.activityMatches[0]),
          ...(response?.data?.predictEmissionsFactors?.confidence && {
            confidence: response.data.predictEmissionsFactors.confidence,
          }),
          ...(response?.data?.predictEmissionsFactors?.explanation && {
            explanation: response.data.predictEmissionsFactors.explanation,
          }),
          exchanges:
            response.data.predictEmissionsFactors?.matchedActivity?.exchanges ??
            [],
        }

        let recommendations = [...(response?.data?.predictEmissionsFactors.recommendations ?? [])];

        let selectedName = emissionsFactorMatchForm.getFieldValue('chemicalName');
        if (initialEmissionsFactor?.activityName && selectedName == selectedItem?.chemicalName) {
          const existingEmissionsFactorMatch = recommendations.find(activity => {
            return activity.activityName == initialEmissionsFactor?.activityName &&
            activity.referenceProduct == initialEmissionsFactor?.referenceProduct &&
            activity.source == initialEmissionsFactor?.source &&
            activity.geography == initialEmissionsFactor?.geography
          })
          if (!existingEmissionsFactorMatch) {
            recommendations = [initialEmissionsFactor, ...recommendations]
          }
        } else {
          recommendations = [emissionsFactorMatch.activityMatch, ...recommendations]
        }

        emissionsFactorMatch.activityMatches =
        recommendations.map(activity => {
              if (
                activity.activityName == emissionsFactorMatch.activityMatch.activityName &&
                activity.referenceProduct == emissionsFactorMatch.activityMatch.referenceProduct &&
                activity.source == emissionsFactorMatch.activityMatch.source &&
                activity.geography == emissionsFactorMatch.activityMatch.geography
              ) {
                return {
                  ...activity,
                  confidence: emissionsFactorMatch.activityMatch.confidence,
                  explanation: emissionsFactorMatch.activityMatch.explanation,
                  exchanges: emissionsFactorMatch.activityMatch.exchanges
                };
              }
              return activity;
            });

        setEmissionsFactorMatches(emissionsFactorMatch.activityMatches)
        setEmissionsFactorMatchesTableKey(uuidv4())

      }
    } catch (error) {
      console.error('Error predicting emissions factors:', error)
      message.error('Failed to predict emissions factors')
    } finally {
      setSearchLoading(false)
    }
  }

  const handleShowIntermediateExchanges = (record) => {
    setSelectedActivityForExchanges(record)
    setIsIntermediateExchangesOpen(true)
  }

  const handleUpdateEmissionsFactor = () => {
    if (!selectedEmissionsFactor) {
      return message.error('Please select an emissions factor')
    }

    onEmissionsFactorUpdate({
      ...selectedEmissionsFactor,
      modified: true,
    })
    onClose()
  }

  const handleExchangesUpdate = (updatedEF) => {
    setSelectedEmissionsFactor((prevEF) => ({
      ...prevEF,
      ...updatedEF,
      modified: true,
    }))

    setEmissionsFactorMatches((prev) =>
      prev.map((ef) =>
        ef.activityName === updatedEF.activityName
          ? { ...ef, ...updatedEF, modified: true }
          : ef
      )
    )
  }

  const columns = [
    {
      title: 'Select',
      fixed: 'left',
      render: (_, record) => (
        <Radio
          checked={
            selectedEmissionsFactor?.activityName === record.activityName &&
            selectedEmissionsFactor?.referenceProduct ===
              record.referenceProduct &&
            selectedEmissionsFactor?.geography === record.geography &&
            selectedEmissionsFactor?.source === record.source
          }
          onChange={() => setSelectedEmissionsFactor(record)}
        />
      ),
    },
    {
      title: 'Activity Name',
      dataIndex: 'activityName',
      render: (text, record) => (
        <Space>
          {text}
          {record.curated && <Tag color="green">Preferred</Tag>}
          {record.modified && <Tag color="green">Customized</Tag>}
        </Space>
      ),
    },
    {
      title: 'Reference Product',
      dataIndex: 'referenceProduct',
    },
    {
      title: 'Geography',
      dataIndex: 'geography',
    },
    {
      title: 'Confidence',
      dataIndex: 'confidence',
      render: (text, record) =>
        text?.length ? (
          <>
            <Tag color="green">{text.toUpperCase()}</Tag>
          </>
        ) : (
          <>N/A</>
        ),
    },
    {
      title: 'Explanation',
      dataIndex: 'explanation',
      render: (text, record) => (text?.length ? <>{text}</> : <>N/A</>),
    },
    {
      title: 'Source',
      dataIndex: 'source',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Action',
      render: (_, record) => (
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleShowIntermediateExchanges(record)}
        />
      ),
      fixed: 'right',
    },
  ]

  const handleCustomEFModalClose = () => {
    setIsAddCustomEFModalOpen(false)
    addCustomEmissionsFactorForm.resetFields()
  }

  const getAvailableEFUnits = () => {
    return [
      { label: 'Kilograms (kg)', value: 'kg' },
      { label: 'Square Meters (m2)', value: 'm2' },
      { label: 'Unit (unit)', value: 'unit' },
      { label: 'Meters (m)', value: 'm' },
      { label: 'Cubic Meters (m3)', value: 'm3' },
      { label: 'Megajoules (MJ)', value: 'MJ' },
      { label: 'Hectares (ha)', value: 'ha' },
      { label: 'Kilowatt Hours (kWh)', value: 'kWh' },
      { label: 'Guest Nights (guest night)', value: 'guest night' },
      { label: 'Square Meters per Year (m*year)', value: 'm*year' },
      { label: 'Hours (hour)', value: 'hour' },
      { label: 'Kilometers (km)', value: 'km' },
      { label: 'Liters (l)', value: 'l' },
      { label: 'Square Meters per Year (m2*year)', value: 'm2*year' },
      { label: 'Kilometers per Year (km*year)', value: 'km*year' },
      { label: 'Metric Tonne-Kilometers (metric ton*km)', value: 'metric ton*km' },
      { label: 'Kilograms per Day (kg*day)', value: 'kg*day' },
      { label: 'Person-Kilometers (person*km)', value: 'person*km' },
    ]
  }

  const handleCreateCustomEF = async () => {


      const activityName = addCustomEmissionsFactorForm.getFieldValue('activityName')
      const referenceProduct = addCustomEmissionsFactorForm.getFieldValue('referenceProduct')
      const unit = addCustomEmissionsFactorForm.getFieldValue('unit')

      await addCustomEmissionsFactorForm.validateFields()

      if (!activityName || !referenceProduct || !unit) {
        return message.error('Please fill in all required fields')
      }



      const emissionsFactorInput = {
        parent_emissions_factor: {
          activity_name: activityName,
          activity_type: activityType,
          unit: unit,
          reference_product_name: referenceProduct,
          geography: selectedItem.geography,
          source: orgName.toUpperCase(),
        },
        exchanges: [],
        elemental_ef_values: []
      }

      try {
        const response = await createEmissionsFactor({
          variables: {
            emissionsFactor: emissionsFactorInput,
          },
        })

        const createdEmissionsFactor = response.data.createEmissionsFactor

        const newEfToAdd = {
          activityName: createdEmissionsFactor.activityName,
          curated: true,
          elementalEfValues: [],
          exchanges: [],
          explanation: "This is a customized match",
          geography: createdEmissionsFactor.geography,
          modified: false,
          referenceProduct: createdEmissionsFactor.referenceProduct,
          source: createdEmissionsFactor.source,
          unit: createdEmissionsFactor.unit
        }

        setEmissionsFactorMatches([newEfToAdd, ...emissionsFactorMatches])
        setSelectedEmissionsFactor(newEfToAdd)
        setEmissionsFactorMatchesTableKey(uuidv4())
        handleCustomEFModalClose()
        handleShowIntermediateExchanges(newEfToAdd)

      } catch (error) {
        console.error('Error creating custom emissions factor:', error)
        message.error('Error creating custom emissions factor:', error)
      }


  }

  return (
    <>
      <Drawer
        title="Select Emissions Factor"
        placement="right"
        width="80%"
        onClose={onClose}
        open={isOpen}
      >
        <Form form={emissionsFactorMatchForm} layout="vertical">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="chemicalName"
                label="Name"
                rules={[
                  { required: true, message: 'name is required' },
                ]}
              >
                <Input placeholder="Enter name" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="geography"
                label="Geography"
                rules={[{ required: true, message: 'Geography is required' }]}
              >
                <Select
                  showSearch
                  allowClear
                  options={countries}
                  placeholder="Select geography"
                  filterOption={(input, option) =>
                    option.value.toUpperCase().includes(input.toUpperCase())
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label=" ">
                <Button
                  type="primary"
                  id='search-ef-button'
                  onClick={handleEmissionsFactorSearch}
                  loading={searchLoading}
                  icon={<SearchOutlined />}
                >
                  Search
                </Button>
                <Divider type='vertical'/>
                <Button
                  type="primary"
                  onClick={() => setIsAddCustomEFModalOpen(true)}
                >
                  <Space>
                    <PlusOutlined />
                    Custom Activity
                  </Space>
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <DataTable
          tableLoading={searchLoading}
          bordered
          key={emissionsFactorMatchesTableKey}
          columns={columns}
          data={emissionsFactorMatches}
        />

        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Button
            type="primary"
            disabled={!selectedEmissionsFactor}
            onClick={handleUpdateEmissionsFactor}
          >
            Update
          </Button>
        </div>
      </Drawer>
      <Modal
        title={'Add Custom Activity'}
        open={isAddCustomEFModalOpen}
        onCancel={handleCustomEFModalClose}
        footer={null}
        width={800}
        destroyOnClose
      >
          <p style={{ textAlign: 'left', fontSize: '16px' }}>
            New Activity
          </p>
          <Divider></Divider>
          <Form
            form={addCustomEmissionsFactorForm}
            layout="vertical"
            style={{ maxWidth: 600 }}
            id="add-custom-emissions-factor-form"
            initialValues={{
              unit: 'kg'
            }}
          >

            <Form.Item
              label="New Activity Name"
              name="activityName"
              rules={[{ required: true, message: 'Activity name is required' }]}
              tooltip="Specify the name of the activity"
            >
              <Input placeholder="eg: Wooden Chair Manufacturing" />
            </Form.Item>

            <Form.Item
              label="New Reference Product"
              name="referenceProduct"
              rules={[{ required: true, message: 'Reference product is required' }]}
              tooltip="Specify the reference product"
            >
              <Input placeholder="eg: Wooden Chair" />
            </Form.Item>

            <Form.Item
              name="unit"
              label="Unit"
              rules={[{ required: true }]}
            >
              <Select options={getAvailableEFUnits()} />
            </Form.Item>

            <Button
                style={{
                  backgroundColor: '#f3c314d4',
                  color: 'white',
                  fontWeight: 600,
                }}
                id='add-custom-ef-submit'
                loading={createEmissionsFactorIsLoading}
                onClick={handleCreateCustomEF}
              >
                Save
              </Button>
          </Form>
      </Modal>

      {selectedActivityForExchanges && (
        <IntermediateExchanges
          isOpen={isIntermediateExchangesOpen}
          onClose={() => setIsIntermediateExchangesOpen(false)}
          parentActivity={{
            activityName: selectedActivityForExchanges.activityName,
            referenceProduct: selectedActivityForExchanges.referenceProduct,
            geography: selectedActivityForExchanges.geography,
            source: selectedActivityForExchanges.source,
            unit: selectedActivityForExchanges.unit,
            exchanges: selectedActivityForExchanges.exchanges,
          }}
          onUpdate={handleExchangesUpdate}
          editMode={editMode}
          orgName={orgName}
        />
      )}
    </>
  )
}

export default EmissionsFactorSelector
