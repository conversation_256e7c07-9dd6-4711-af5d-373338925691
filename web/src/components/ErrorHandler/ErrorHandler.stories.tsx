// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import ErrorHandler from './ErrorHandler'

const meta: Meta<typeof ErrorHandler> = {
  component: ErrorHandler,
}

export default meta

type Story = StoryObj<typeof ErrorHandler>

export const Primary: Story = {}
