import { Select, Space, Switch, Divider, Drawer, Avatar } from 'antd'
import { useEffect, useState } from 'react'
import { SettingOutlined } from '@ant-design/icons'
import { useMutation } from '@redwoodjs/web'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import './style.css'

const SET_DEFAULT_IMPACT_FACTOR = gql`
  mutation SetDefaultImpactFactor(
    $categoryName: String!
    $indicator: String!
    $lciaMethod: String!
    $unit: String!
  ) {
    setDefaultImpactFactor(
      categoryName: $categoryName
      indicator: $indicator
      lciaMethod: $lciaMethod
      unit: $unit
    )
  }
`

const SET_CALCULATE_EMISSIONS_PER_UNIT = gql`
  mutation SetCalculateEmissionsPerUnit($calculatePerUnit: Boolean!) {
    setCalculateEmissionsPerUnit(calculatePerUnit: $calculatePerUnit)
  }
`

const ImpactFactorSelector = () => {
  const [impactFactors, setImpactFactors] = useState(null)
  const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false)
  const [
    calculateEmissionsPerUnitEnabled,
    setCalculateEmissionsPerUnitEnabled,
  ] = useState(false)
  const [selectedImpactFactorValue, setSelectedImpactFactorValue] = useState({
    lciaMethod: 'ReCiPe 2016 v1.03, midpoint (H)',
    categoryName: 'climate change',
    indicator: 'global warming potential (GWP100)',
    unit: 'kg CO2-Eq',
  })

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const tenantID =
  orgMemberInfo && orgMemberInfo.urlSafeOrgName
    ? orgMemberInfo.urlSafeOrgName
    : null



  useEffect(() => {
    if (userMetadata?.user?.metadata?.defaultImpactFactor?.[tenantID]) {
      setSelectedImpactFactorValue(
        userMetadata.user.metadata.defaultImpactFactor[tenantID]
      )
    }

    if (userMetadata?.user?.metadata?.calculateEmissionsPerUnit) {
      setCalculateEmissionsPerUnitEnabled(true)
    }

    if (orgMemberInfo?.orgMetadata?.supported_impact_factors) {
      const _impactFactors = []
      try {
        Object.keys(
          orgMemberInfo.orgMetadata.supported_impact_factors
        ).forEach((method) => {
          orgMemberInfo.orgMetadata.supported_impact_factors[method].forEach(
            (category) => {
              _impactFactors.push({
                value: JSON.stringify({
                  lciaMethod: method,
                  categoryName: category.name,
                  indicator: category.indicator,
                  unit: category.unit,
                }),
                label: category.alias ? category.alias : category.name,
              })
            }
          )
        })
        setImpactFactors(_impactFactors)
      } catch (e) {
        console.error(`Error parsing supported impact factors: ${e}`)
      }
    }
  }, [userMetadata, orgMemberInfo])

  const onSettingsDrawerClose = () => {
    setSettingsDrawerVisible(false)
  }

  const [setDefaultImpactFactor, { loading: setDefaultImpactFactorIsLoading }] =
    useMutation(SET_DEFAULT_IMPACT_FACTOR)

  const [
    setCalculateEmissionsPerUnit,
    { loading: setCalculateEmissionsPerUnitIsLoading },
  ] = useMutation(SET_CALCULATE_EMISSIONS_PER_UNIT)

  const handleChange = async (value: string) => {
    const impactFactor = JSON.parse(value)
    await setDefaultImpactFactor({
      variables: {
        categoryName: impactFactor.categoryName,
        indicator: impactFactor.indicator,
        lciaMethod: impactFactor.lciaMethod,
        unit: impactFactor.unit,
      },
    })
    setSelectedImpactFactorValue(impactFactor)
    window.location.reload()
  }

  const handleCalculateEmissionsPerUnitChange = async (checked) => {
    await setCalculateEmissionsPerUnit({
      variables: {
        calculatePerUnit: checked,
      },
    })
    setCalculateEmissionsPerUnitEnabled(checked)
    setTimeout(() => {
      window.location.reload()
    }, 500)
  }

  const getCurrentImpactFactorSelectValue = () => {
    if (!selectedImpactFactorValue) return 'Select Impact Factor'

    const matchingImpactFactor = impactFactors?.find(factor => {
      const parsedValue = JSON.parse(factor.value)
      return (
        parsedValue.lciaMethod === selectedImpactFactorValue.lciaMethod &&
        parsedValue.categoryName === selectedImpactFactorValue.categoryName &&
        parsedValue.indicator === selectedImpactFactorValue.indicator &&
        parsedValue.unit === selectedImpactFactorValue.unit
      )
    })

    return matchingImpactFactor ? matchingImpactFactor.value : selectedImpactFactorValue.categoryName
  }

  return (
    <>
      <Avatar
        style={{
          color: 'white',
          cursor: 'pointer',
          marginRight: 16,
        }}
        className="help-and-support-menu"
        icon={<SettingOutlined />}
        onClick={() => setSettingsDrawerVisible(true)}
      />
      <Drawer
        title="Settings"
        placement="right"
        onClose={onSettingsDrawerClose}
        open={settingsDrawerVisible}
      >
        <div>
          <h4 className="mb-2 font-medium">Display Settings</h4>
          <Divider />
          <div className='ml-4'>
          {impactFactors ? (
            <>
              <h4 className="mb-2">Impact Category</h4>
              <Space
                direction="vertical"
                className='mb-4'
                style={{ width: '100%' }}
                size="small"
              >
                <Select
                  loading={setDefaultImpactFactorIsLoading}
                  disabled={setDefaultImpactFactorIsLoading}
                  style={{ width: '100%', whiteSpace: 'normal' }}
                  value={getCurrentImpactFactorSelectValue()}
                  onChange={(value) => handleChange(value)}
                  options={impactFactors}
                />
              </Space>
            </>
          ) : null}
          <Divider />
          <h4 className="mb-4 mt-12">Impact Display Quantity</h4>
          <Space direction="vertical" style={{ width: '100%' }} size="small">
            <Switch
              checkedChildren="Per unit"
              unCheckedChildren="Total units"
              checked={calculateEmissionsPerUnitEnabled}
              onChange={handleCalculateEmissionsPerUnitChange}
              loading={setCalculateEmissionsPerUnitIsLoading}
            />
          </Space>
          </div>
        </div>
      </Drawer>
    </>
  )
}

export default ImpactFactorSelector
