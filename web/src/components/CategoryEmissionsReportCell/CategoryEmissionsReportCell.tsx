import React, { useRef } from 'react'
import { useReactToPrint } from 'react-to-print'
import type { DashboardMetricsQuery } from 'types/graphql'
import type { CellSuccessProps, CellFailureProps } from '@redwoodjs/web'
import { Image, Row, Col, Tag, Flex, Card, Button } from 'antd'
import moment from 'moment'
import { saveAs } from 'file-saver'
import DataTable from '../DataTable/DataTable'
import {
  ArrowLeftOutlined,
  PrinterOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import { Link, navigate, routes } from '@redwoodjs/router'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import { Column } from '@ant-design/charts'
import './print.css'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import { renderImpactFactorUnit } from 'src/utils/helper'

export const QUERY = gql`
  query DashboardMetricsQuery {
    getDashboardMetrics {
      lifeCycleEmissionsByProductcategory
    }
  }
`

export const Loading = () => <div>Loading...</div>

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const Success = (props: CellSuccessProps<DashboardMetricsQuery>) => {
  if (!props) {
    return <ErrorHandler />
  }

  const dashboardMetrics = props.getDashboardMetrics

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const orgName =
    orgMemberInfo && orgMemberInfo.orgName ? orgMemberInfo.orgName : null

  const componentRef = useRef()
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
  })

  let lifeCycleStagesMap = {
    rawMaterialsEmissions: 'Raw Materials',
    packagingMaterialEmissions: 'Packaging',
    manufacturingEmissions: 'Manufacturing',
    distributionEmissions: 'Distribution',
    consumerUseEmissions: 'Consumer Use',
    eolEmissions: 'End-of-Life',
  }

  const colorPalette = {
    'Raw Materials': '#ffde1aff',
    Packaging: '#ffde1aff',
    Manufacturing: '#ffce00ff',
    Distribution: '#ff8d00ff',
    'Consumer Use': '#ff7400ff',
    'End-of-Life': '#ff7400ff',
  }

  const productCategoryEmissionsData = Object.keys(
    dashboardMetrics.lifeCycleEmissionsByProductcategory
  ).reduce((acc, category) => {
    const categoryData =
      dashboardMetrics.lifeCycleEmissionsByProductcategory[category]
    return acc.concat(
      Object.keys(categoryData).map((type) => ({
        category,
        type: lifeCycleStagesMap[type],
        value: parseFloat(categoryData[type].toFixed(3)),
      }))
    )
  }, [])

  const columnStackedChartConfig = {
    data: productCategoryEmissionsData,
    isStack: true,
    xField: 'category',
    yField: 'value',
    seriesField: 'type',
    autoFit: true,
    height: 485,
    theme: {
      colors10: [
        colorPalette['Raw Materials'],
        colorPalette['Packaging'],
        colorPalette['Manufacturing'],
        colorPalette['Distribution'],
        colorPalette['Consumer Use'],
        colorPalette['End-of-Life'],
      ],
    },
    interactions: [
      {
        type: 'active-region',
        enable: false,
      },
    ],
    connectedArea: {
      style: (oldStyle, element) => {
        return {
          fill: 'rgba(0,0,0,0.25)',
          stroke: oldStyle.fill,
          lineWidth: 0.5,
        }
      },
    },
  }

  const categoryEmissionsTabulatedData = Object.keys(
    dashboardMetrics.lifeCycleEmissionsByProductcategory
  ).map((product) => {
    const emissions =
      dashboardMetrics.lifeCycleEmissionsByProductcategory[product]

    const totalEmissions = Object.keys(emissions).reduce((total, key) => {
      return total + emissions[key]
    }, 0)

    return {
      category: product,
      ingredients: emissions.rawMaterialsEmissions.toFixed(4),
      packaging: emissions.packagingMaterialEmissions.toFixed(4),
      manufacturing: emissions.manufacturingEmissions.toFixed(4),
      distribution: emissions.distributionEmissions.toFixed(4),
      consumerUse: emissions.consumerUseEmissions.toFixed(4),
      eol: emissions.eolEmissions.toFixed(4),
      totalEmissions: totalEmissions.toFixed(4),
    }
  })

  const columns = [
    {
      title: 'Category',
      dataIndex: 'category',
    },
    {
      title: `Raw Materials (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'ingredients',
    },
    {
      title: `Packaging (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'packaging',
    },
    {
      title: `Manufacturing (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'manufacturing',
    },
    {
      title: `Distribution (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'distribution',
    },
    {
      title: `Consumer Use (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'consumerUse',
    },
    {
      title: `End of Life (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'eol',
    },
    {
      title: `Total (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text: string) => <b>{text}</b>,
    },
  ]

  const exportToCsv = (emissions) => {
    const headers = columns.map((x) => x.title)
    const csvData = [headers]

    emissions.forEach((emission) => {
      const row = [
        emission.category,
        emission.ingredients,
        emission.packaging,
        emission.manufacturing,
        emission.distribution,
        emission.consumerUse,
        emission.eol,
        emission.totalEmissions,
      ]
      csvData.push(row)
    })

    const blob = new Blob([csvData.join('\n')], {
      type: 'text/csv;charset=utf-8',
    })
    saveAs(blob, 'CarbonBright_Product_Emissions_By_Category_Report.csv')
  }

  return (
    <>
      <Row style={{ marginTop: '10px' }}>
        <Col flex="auto">
          <Button
            type="link"
            style={{ color: 'black' }}
            onClick={() => navigate(routes.reports())}
          >
            <b>
              <ArrowLeftOutlined /> &nbsp;Back
            </b>
          </Button>
        </Col>
        <Col flex="0">
          <Flex gap="small">
            <Button
              type="link"
              style={{ color: 'black' }}
              onClick={handlePrint}
            >
              <p style={{ fontSize: '18px' }}>
                <PrinterOutlined />
              </p>
            </Button>
            <Button
              type="link"
              style={{ color: 'black' }}
              onClick={() => exportToCsv(categoryEmissionsTabulatedData)}
            >
              <p style={{ fontSize: '18px' }}>
                <DownloadOutlined />
              </p>
            </Button>
          </Flex>
        </Col>
      </Row>
      <Card ref={componentRef} style={{ marginTop: '10px' }}>
        <Row>
          <Col flex="auto">
            <h1 style={{ fontSize: '18px', fontWeight: 'bold' }}>
              Product Emissions by Category
            </h1>
          </Col>
          <Col flex="0">
            <Image
              width={150}
              preview={false}
              alt="CarbonBright"
              src="/images/logo.svg"
            />
          </Col>
        </Row>
        <Row>
          <Col flex="auto">
            <p style={{}}>Company: {orgName}</p>
          </Col>
          <Col flex="12">
            <p style={{}}>Creation Date: {moment().format('DD/MM/YYYY')}</p>
          </Col>
        </Row>
        <Card style={{ marginTop: '10px' }} title="Product Category Emissions">
          <Column {...columnStackedChartConfig} />
        </Card>
        <DataTable
          style={{ marginTop: '20px' }}
          key={categoryEmissionsTabulatedData.length}
          data={categoryEmissionsTabulatedData}
          columns={columns}
          paginate={false}
          scroll={null}
        />
      </Card>
    </>
  )
}
