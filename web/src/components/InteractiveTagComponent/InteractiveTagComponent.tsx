import { AutoComplete, Input, Spin, Tag } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useMemo, useState } from 'react'

const InteractiveTagComponent = ({ record, handleSave, data, loading }) => {
  const [tags, setTags] = useState(record.tags || [])
  const [inputVisible, setInputVisible] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [editInputIndex, setEditInputIndex] = useState(-1)
  const [editInputValue, setEditInputValue] = useState('')

  const handleClose = (removedTag) => {
    const newTags = tags.filter((tag) => tag !== removedTag)
    setTags(newTags)
    handleSave({ ...record, tags: newTags })
  }

  const showInput = () => {
    setInputVisible(true)
  }

  const handleInputChange = (value) => {
    setInputValue(typeof value === 'string' ? value : value.target.value)
  }

  const handleInputConfirm = (value) => {
    const _inputValue = (typeof value === 'string' ? value : inputValue).trim()
    if (_inputValue && tags.indexOf(_inputValue) === -1) {
      const newTags = [...tags, _inputValue]
      setTags(newTags)
      handleSave({ ...record, tags: newTags })
    }
    setInputVisible(false)
    setInputValue('')
  }

  const handleEditInputChange = (e) => {
    setEditInputValue(e.target.value)
  }

  const handleEditInputConfirm = () => {
    const newTags = [...tags]
    newTags[editInputIndex] = editInputValue
    setTags(newTags)
    setEditInputIndex(-1)
    setEditInputValue('')
    handleSave({ ...record, tags: newTags })
  }

  const suggestions = useMemo(() => {
    const allTags = data.flatMap((product) => product.tags || [])
    return [...new Set(allTags)]
  }, [data])

  return (
    <Spin spinning={loading}>
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '8px',
          maxWidth: '200px', // Adjust this value based on your table column width
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {tags.map((tag, index) => {
          if (editInputIndex === index) {
            return (
              <Input
                className='tag-input'
                key={tag}
                size="small"
                style={{ width: '100%' }}
                value={editInputValue}
                onChange={handleEditInputChange}
                onBlur={handleEditInputConfirm}
                onPressEnter={handleEditInputConfirm}
              />
            )
          }
          return (
            <Tag
              key={tag}
              closable
              style={{ margin: 0 }}
              onClick={(e) => {
                e.preventDefault()
                setEditInputIndex(index)
                setEditInputValue(tag)
              }}
              onClose={(e) => {
                e.preventDefault()
                handleClose(tag)
              }}
            >
              {tag}
            </Tag>
          )
        })}
        {inputVisible ? (
          <AutoComplete
            style={{ width: '100%' }}
            options={suggestions.map((item) => ({ value: item }))}
            filterOption={(inputValue, option) =>
              option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !==
              -1
            }
            onSelect={handleInputConfirm}
            onSearch={handleInputChange}
          >
            <Input
              className="tag-input"
              type="text"
              size="small"
              style={{ width: '100%' }}
              value={inputValue}
              onChange={handleInputChange}
              onBlur={handleInputConfirm}
              onPressEnter={handleInputConfirm}
            />
          </AutoComplete>
        ) : (
          <Tag
            onClick={showInput}
            style={{
              background: '#fff',
              borderStyle: 'dashed',
              margin: 0,
              cursor: 'pointer',
            }}
            className="add-tag-button"
          >
            <PlusOutlined />
          </Tag>
        )}
      </div>
    </Spin>
  )
}

export default InteractiveTagComponent
