.copilot-button {
  font-weight: 600;
}

.ant-steps-item-finish .ant-steps-item-tail::after {
  background-color: #FFC000 !important;
}

.ant-steps-item-icon {
  background-color: #a09f9d !important;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background-color: #FFC000 !important;
  border-color: white !important;
}

.ant-steps-item-active .ant-steps-item-icon {
  background-color: #FFC000 !important;
  border-color: white !important;
  font-weight: 600;
}

.ant-steps-item-title p {
  border-color: unset;
  border: 1px solid #c2c1c0 !important;
  border-radius: 10px;
}

.ant-steps-item-finish .ant-steps-item-title p {
  border-color: unset;
  border: 1px solid #faad14 !important;
  border-radius: 10px;
}

.ant-steps-item-active .ant-steps-item-title p {
  font-weight: 600;
  border-color: unset;
  border: 1px solid #faad14 !important;
  border-radius: 10px;
}

.ant-steps-icon {
  color: white !important;
}

@keyframes breathe {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
  }

  50% {
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(255, 255, 0, 1);
  }
}

.prediction-loading-icon {
  width: 25px !important;
  background-size: cover;
  animation: breathe 1.5s infinite;
}

.ant-form-item .ant-form-item-label {
  overflow: visible;
  white-space: pre-line;
}

.ant-switch:not(.ant-switch-checked) {
  background-color: grey !important;
}