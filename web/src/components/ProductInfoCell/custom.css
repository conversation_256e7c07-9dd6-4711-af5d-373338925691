/*
Custom CSS
Component: ProductInfoCell
Overrides the default styles of the ProductInfoCell - Descriptions component
*/

.no-border-table .ant-table-tbody>tr>td {
  border: none;
}

.no-border-table .ant-table-thead>tr>th {
  background: transparent;
  border-bottom: 0 !important;
}

.ant-descriptions .ant-descriptions-row {
  border-bottom: 0 !important;
}

.ant-descriptions-bordered>.ant-descriptions-view {
  border: none !important;
}

.ant-card .ant-card-head {
  border-bottom: 0 !important;
}

#product-info-card.ant-card-bordered {
  border: 1px solid transparent;
}

.ant-descriptions-row>.ant-descriptions-item-label {
  background-color: transparent !important;
  border-inline-end: 0 !important;
  width: 20% !important;
  padding: 12px 24px !important;
}

.ant-progress-outer {
  width: 400px !important;
}