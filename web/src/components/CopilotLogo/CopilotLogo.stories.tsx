// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import Co<PERSON>lot<PERSON><PERSON> from './CopilotLogo'

const meta: Meta<typeof CopilotLogo> = {
  component: CopilotLogo,
}

export default meta

type Story = StoryObj<typeof CopilotLogo>

export const Primary: Story = {}
