// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import FileViewer from './FileViewer'

const meta: Meta<typeof FileViewer> = {
  component: FileViewer,
}

export default meta

type Story = StoryObj<typeof FileViewer>

export const Primary: Story = {}
