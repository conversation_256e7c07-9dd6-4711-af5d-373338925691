import { navigate } from '@redwoodjs/router'
import {
  Modal,
  Carousel,
  Row,
  Col,
  Form,
  Input,
  Button,
  message as Message,
} from 'antd'
import './style.css'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import { useEffect, useState } from 'react'
import { useMutation, useQuery } from '@redwoodjs/web'

const UPDATE_TRIAL_MUTATION = gql`
  mutation UpgradeTrial {
    upgradeTrial
  }
`

const UpgradePlan = ({
  visible,
  onClose,
  message = 'This is a PRO feature. Please upgrade your plan to access all PRO features.',
}) => {
  const { userMetadata } = useAuth()

  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const [trialUpgradeRequested, setTrialUpgradeRequested] = useState(false)

  useEffect(() => {
    if (orgMemberInfo?.orgMetadata?.upgradeRequested) {
      setTrialUpgradeRequested(true)
    }
  }, [userMetadata])

  const [upgradeTrial, { loading: updateTrialIsLoading }] = useMutation(
    UPDATE_TRIAL_MUTATION
  )

  const handleUpgradeTrial = async () => {
    try {
      await upgradeTrial()
      setTrialUpgradeRequested(true)
      setTimeout(() => {
        window.location.reload()
      }, 5000)
    } catch (error) {
      Message.error('Unable to submit your request. Please try again.')
    }
  }

  return (
    <Modal
      style={{ marginTop: '10%' }}
      visible={visible}
      title={
        trialUpgradeRequested
          ? 'Upgrade Request Submitted'
          : `Hey ${
              userMetadata?.user?.firstName ?? userMetadata?.user?.email
            }, Upgrade Your Plan`
      }
      closeIcon={onClose ? true : false}
      onOk={handleUpgradeTrial}
      okText="Upgrade"
      okButtonProps={{
        style: {
          backgroundColor: '#f3c314d4',
          display: trialUpgradeRequested ? 'none' : 'inline-block',
        },
        loading: updateTrialIsLoading,
      }}
      cancelButtonProps={{
        style: { display: !onClose ? 'none' : 'inline-block' },
        loading: updateTrialIsLoading,
      }}
      onCancel={onClose}
    >
      <div
        id="upgrade-trial-carousel"
        style={{
          display: trialUpgradeRequested ? 'none' : 'block',
        }}
      >
        <p>{message}</p>
        <Carousel autoplay style={{ marginTop: 20 }} arrows infinite={true}>
          <div>
            <img src="/images/tour/tour-3.png" alt="upgrade-plan-1" />
          </div>
          <div>
            <img src="/images/tour/tour-4.png" alt="upgrade-plan-1" />
          </div>
        </Carousel>
      </div>

      <div
        id="upgrade-trial-submitted"
        style={{
          display: trialUpgradeRequested ? 'block' : 'none',
        }}
      >
        <p>
          Your request to upgrade your plan has been submitted. We will get back
          to you shortly.
        </p>
      </div>
    </Modal>
  )
}

export default UpgradePlan
