// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import UpgradePlan from './UpgradePlan'

const meta: Meta<typeof UpgradePlan> = {
  component: UpgradePlan,
}

export default meta

type Story = StoryObj<typeof UpgradePlan>

export const Primary: Story = {}
