import type { GetEmissionsFactorsQuery } from 'types/graphql'
import {
  type CellSuccessProps,
  type CellFailureProps,
  useQuery,
} from '@redwoodjs/web'
import {
  Button,
  notification,
  Skeleton,
  Spin,
  Tag,
  Tooltip,
  Space,
  Input,
  Select,
  Row,
  Col,
} from 'antd'
import { FileOutlined, EditOutlined, CopyOutlined, SearchOutlined, FilterOutlined, PlusOutlined } from '@ant-design/icons'
import DataTable from '../DataTable/DataTable'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import { useEffect, useMemo, useState } from 'react'
import './style.css'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import { gql, useLazyQuery } from '@apollo/client'
import IntermediateExchanges from '../IntermediateExchanges/IntermediateExchanges'
import CreateCustomEmissionsFactor from '../CreateCustomEmissionsFactor/CreateCustomEmissionsFactor'

export const QUERY = gql`
  query GetEmissionsFactors($activityName: String, $geography: [String], $source: [String], $unit: [String]) {
    getEmissionsFactors(
      activityName: $activityName
      geography: $geography
      source: $source
      unit: $unit
    ) {
      efId
      activityName
      description
      referenceProduct
      geography
      source
      unit
      isTenant
    }
  }
`

export const GET_ACTIVITY_SOURCES = gql`
  query GetActivitySources {
    getActivitySources {
      source
    }
  }
`

const LoadingSkeleton = (
  <>
    <div>
      <Spin size="large" tip="Loading...">
        <Skeleton paragraph={{ rows: 30 }} active />
      </Spin>
    </div>
  </>
)

export const Loading = () => LoadingSkeleton

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const beforeQuery = () => {
  return {
    variables: {
      activityName: "",
      geography: [],
      source: [],
      unit: []
    }
  }
}

export const Success = ({ getEmissionsFactors }: CellSuccessProps<GetEmissionsFactorsQuery>) => {
  if (!getEmissionsFactors) {
    return <ErrorHandler />
  }

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  const sortEmissionsFactors = (factors) => {
    return factors
      .map((factor, index) => ({
        key: index + 1,
        ...factor,
        originalIndex: index,
      }))
      .sort((a, b) => {
        const nameA = a.activityName || ''
        const nameB = b.activityName || ''

        if (nameA < nameB) return -1
        if (nameA > nameB) return 1

        return a.originalIndex - b.originalIndex
      })
  }

  const [selectedFactors, setSelectedFactors] = useState([])
  const [tableData, setTableData] = useState(sortEmissionsFactors(getEmissionsFactors))
  const [tableKey, setTableKey] = useState(Date.now())
  const [filteredData, setFilteredData] = useState(tableData)
  const [isIntermediateExchangesOpen, setIsIntermediateExchangesOpen] = useState(false)
  const [selectedActivity, setSelectedActivity] = useState(null)
  const [isRefetching, setIsRefetching] = useState(false)
  const [showCreateCustomEF, setShowCreateCustomEF] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [geographyFilter, setGeographyFilter] = useState([])
  const [unitFilter, setUnitFilter] = useState([])
  const [sourceFilter, setSourceFilter] = useState([])
  const [activitySources, setActivitySources] = useState([])

  const {
    refetch: refetchEmissionsFactors,
    data: refetchedData,
    loading: refetchedDataIsLoading,
  } = useQuery(QUERY, {
    variables: {
      activityName: "",
      geography: [],
      source: [],
      unit: []
    },
    skip: true, //skip initial automatic query
    notifyOnNetworkStatusChange: true,
  })

  const { loading: sourcesLoading } = useQuery(
    GET_ACTIVITY_SOURCES,
    {
      onCompleted: (data) => {
        if (data?.getActivitySources) {
          const sources = data.getActivitySources;
          setActivitySources(sources);
        }
      },
      onError: (error) => {
        notification.error({
          message: 'Error fetching activity sources',
          description: error.message,
        })
      },
    }
  )

  useEffect(() => {
    if (refetchedData && isRefetching) {
      const sortedData = sortEmissionsFactors(refetchedData.getEmissionsFactors)
      setTableData(sortedData)
      setFilteredData(sortedData)
      setTableKey(Date.now())
      setIsRefetching(false)
    }
  }, [refetchedData, isRefetching])

  const handleRefetchEmissionsFactors = async () => {
    try {
      setSelectedFactors([])
      setIsRefetching(true)

      const result = await refetchEmissionsFactors()

      if (result && result.data) {
        setTableData(sortEmissionsFactors(result.data.getEmissionsFactors))
        setFilteredData(sortEmissionsFactors(result.data.getEmissionsFactors))
        setTableKey(Date.now())
      }

      setIsRefetching(false)
    } catch (error) {
      console.error('Error refetching activity:', error)
      setIsRefetching(false)
      notification.error({
        message: 'Error refreshing data',
        description: 'Failed to refresh activity data.',
      })
    }
  }

  const columns = useMemo(
    () => [
      {
        title: 'Activity Name',
        dataIndex: 'activityName',
        sorter: true,
        width: '20%',
        fixed: 'left',
      },
      {
        title: 'Description',
        dataIndex: 'description',
        sorter: true,
        width: '15%',
      },
      {
        title: 'Reference Product',
        dataIndex: 'referenceProduct',
        sorter: true,
        width: '15%',
      },
      {
        title: 'Geography',
        dataIndex: 'geography',
        sorter: true,
        width: '10%',
        filters: (() => {
          const allGeographies = Array.from(
            new Set(tableData.map((factor) => factor.geography).filter(Boolean))
          )
          return allGeographies.map((geography) => ({ text: geography, value: geography }))
        })(),
        filterSearch: true,
      },
      {
        title: 'Unit',
        dataIndex: 'unit',
        sorter: true,
        width: '10%',
        filters: (() => {
          const allUnits = Array.from(
            new Set(tableData.map((factor) => factor.unit).filter(Boolean))
          )
          return allUnits.map((unit) => ({ text: unit, value: unit }))
        })(),
        filterSearch: true,
      },
      {
        title: 'Source',
        dataIndex: 'source',
        sorter: true,
        render: (text: string) => (
          <Tag color="blue">{text}</Tag>
        ),
        width: '15%',
        filters: (() => {
          const allSources = Array.from(
            new Set(tableData.map((factor) => factor.source).filter(Boolean))
          )
          return allSources.map((source) => ({ text: source, value: source }))
        })(),
        filterSearch: true,
      },
      {
        title: 'Actions',
        key: 'actions',
        width: '5%',
        fixed: 'right',
        render: (_, record) => (
          <Space>
            <Tooltip title={record.source == tenantID.toUpperCase() ? "Edit Activity" : "Clone the activity to make edits"}>
              <Button
                type="text"
                icon={<EditOutlined />}
                size="small"
                disabled={record.source !== tenantID.toUpperCase()}
                onClick={(e) => {
                  e.stopPropagation();
                  if (record.isTenant) {
                    setSelectedActivity(record);
                    setIsIntermediateExchangesOpen(true);
                  }
                }}
              />
            </Tooltip>
            {
              <Tooltip title="Clone as Custom Activity">
                <Button
                  type="text"
                  icon={<CopyOutlined />}
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedActivity(record);
                    setShowCreateCustomEF(true);
                  }}
                />
              </Tooltip>
            }
          </Space>
        ),
      },
    ],
    [tableData, selectedFactors, filteredData]
  )

  const handleRowClick = (record, index, event) => {
    const clickedCell = event.target.closest('td')
    const row = clickedCell.parentElement
    const isLastColumn = clickedCell === row.lastElementChild

    if (!isLastColumn) {
      setSelectedActivity(record)
      setIsIntermediateExchangesOpen(true)
    }
  }

  const handleSearch = async () => {

    try {
      setIsSearching(true)

      const result = await refetchEmissionsFactors({
        activityName: searchValue.trim(),
        geography: geographyFilter,
        source: sourceFilter,
        unit: unitFilter
      })

      if (result && result.data) {
        const sortedData = sortEmissionsFactors(result.data.getEmissionsFactors)
        setTableData(sortedData)
        setFilteredData(sortedData)
        setTableKey(Date.now())

        if (sortedData.length === 0) {
          notification.info({
            message: 'No results found',
            description: searchValue.trim()?.length ? `No activities found for "${searchValue}"` : 'No activities found',
          })
        }
      }

      setIsSearching(false)
    } catch (error) {
      console.error('Error searching activities:', error)
      setIsSearching(false)
      notification.error({
        message: 'Error searching',
        description: 'Failed to search activities.',
      })
    }
  }

  const handleSearchInputChange = (e) => {
    setSearchValue(e.target.value)
  }

  const handleSearchKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }


  const geographyOptions = useMemo(() => {
    const predefinedGeographies = [
      'RER', 'RoW', 'GLO', 'US', 'CH', 'IN', 'UN-EASIA', 'DE', 'EC', 'CA-QC',
      'ZA', 'CN', 'GB', 'IAI Area, Asia, without China and GCC', 'IAI Area, EU27 & EFTA',
      'IAI Area, Russia & RER w/o EU27 & EFTA', 'IAI Area, South America', 'RNA',
      'UN-OCEANIA', 'CA', 'IAI Area, Africa', 'IAI Area, Gulf Cooperation Council',
      'IAI Area, North America', 'RAF', 'RER w/o RU', 'RLA', 'RU', 'SAS', 'UN-SEASIA',
      'AT', 'ES', 'PE', 'RAS', 'SE', 'DK', 'CL', 'IT', 'AU', 'BR', 'Canada without Quebec',
      'FR', 'TR', 'CO', 'PH', 'CR', 'Europe without Switzerland', 'AU-NSW', 'AU-QLD',
      'AU-SA', 'AU-TAS', 'AU-VIC', 'AU-WA', 'BD', 'IN-MH', 'IN-UP', 'JP', 'KR', 'IL',
      'NL', 'TN', 'CA-ON', 'US-LA', 'KZ', 'NA', 'BR-AC', 'BR-AM', 'BR-AP', 'BR-BA',
      'BR-CE', 'BR-DF', 'BR-ES', 'BR-GO', 'BR-MA', 'BR-MG', 'BR-MT', 'BR-PA', 'BR-PI',
      'BR-RO', 'BR-RR', 'BR-RS', 'BR-SE', 'BR-TO', 'BE', 'GH', 'ID', 'AR', 'TH', 'UA',
      'CI', 'HN', 'KE', 'MY', 'VN', 'BR-SP', 'RoE', 'North America without Quebec', 'ZM',
      'PT', 'IN-OR', 'CM', 'IS', 'MX', 'NI', 'NZ', 'SV', 'US-HICC', 'WECC', 'CA-AB',
      'CA-SK', 'CA-MB', 'NO', 'CN-GD', 'CN-SH', 'CZ', 'HU', 'IN-TN', 'LT', 'LV', 'PL',
      'US-SERC', 'US-WECC', 'BG', 'BR-North-eastern grid', 'BR-Northern grid',
      'BR-Southern grid', 'CA-NB', 'CA-NS', 'CA-PE', 'CN-AH', 'CN-BJ', 'CN-CQ', 'CN-FJ',
      'CN-GS', 'CN-GX', 'CN-GZ', 'CN-HA', 'CN-HB', 'CN-HE', 'CN-HL', 'CN-HN', 'CN-HU',
      'CN-JL', 'CN-JS', 'CN-JX', 'CN-LN', 'CN-NM', 'CN-NX', 'CN-QH', 'CN-SA', 'CN-SC',
      'CN-SD', 'CN-SX', 'CN-TJ', 'CN-XJ', 'CN-XZ', 'CN-YN', 'CN-ZJ', 'EE', 'FI', 'HR',
      'IE', 'IN-AP', 'IN-AS', 'IN-BR', 'IN-CT', 'IN-DL', 'IN-GJ', 'IN-HR', 'IN-JH',
      'IN-KA', 'IN-MP', 'IN-PB', 'IN-RJ', 'IN-WB', 'TW', 'TZ', 'US-ASCC', 'US-MRO',
      'US-NPCC', 'US-RFC', 'US-TRE', 'BA', 'CA-BC', 'CA-NT', 'CA-YK', 'GR', 'LU', 'RO',
      'RS', 'SI', 'SK', 'IN-HP', 'IN-JK', 'IN-KL', 'IN-ML', 'IN-MN', 'IN-NL', 'IN-UT',
      'MK', 'CA-NF'
    ];

    const dataGeographies = Array.from(
      new Set(tableData.map((factor) => factor.geography).filter(Boolean))
    );

    const allGeographies = Array.from(new Set([...predefinedGeographies, ...dataGeographies])).sort();

    return allGeographies.map(geography => ({ label: geography, value: geography }));
  }, [tableData]);

  const getAvailableEFUnits = () => {
    return [
      { label: 'Kilograms (kg)', value: 'kg' },
      { label: 'Square Meters (m2)', value: 'm2' },
      { label: 'Unit (unit)', value: 'unit' },
      { label: 'Meters (m)', value: 'm' },
      { label: 'Cubic Meters (m3)', value: 'm3' },
      { label: 'Megajoules (MJ)', value: 'MJ' },
      { label: 'Hectares (ha)', value: 'ha' },
      { label: 'Kilowatt Hours (kWh)', value: 'kWh' },
      { label: 'Guest Nights (guest night)', value: 'guest night' },
      { label: 'Square Meters per Year (m*year)', value: 'm*year' },
      { label: 'Hours (hour)', value: 'hour' },
      { label: 'Kilometers (km)', value: 'km' },
      { label: 'Liters (l)', value: 'l' },
      { label: 'Square Meters per Year (m2*year)', value: 'm2*year' },
      { label: 'Kilometers per Year (km*year)', value: 'km*year' },
      { label: 'Metric Tonne-Kilometers (metric ton*km)', value: 'metric ton*km' },
      { label: 'Kilograms per Day (kg*day)', value: 'kg*day' },
      { label: 'Person-Kilometers (person*km)', value: 'person*km' },
    ]
  }

  const unitOptions = useMemo(() => {
    const predefinedUnits = getAvailableEFUnits();

    const dataUnits = Array.from(
      new Set(tableData.map((factor) => factor.unit).filter(Boolean))
    ).map(unit => ({ label: unit, value: unit }));

    const unitMap = new Map();

    predefinedUnits.forEach(unit => {
      unitMap.set(unit.value, unit);
    });
    dataUnits.forEach(unit => {
      if (!unitMap.has(unit.value)) {
        unitMap.set(unit.value, unit);
      }
    });

    return Array.from(unitMap.values()).sort((a, b) => a.label.localeCompare(b.label));
  }, [tableData]);

  const sourceOptions = useMemo(() => {
    if (activitySources && activitySources.length > 0) {
      return activitySources
        .map(source => ({ label: source.source, value: source.source }))
        .sort((a, b) => a.label.localeCompare(b.label));
    }

    const dataSources = Array.from(
      new Set(tableData.map((factor) => factor.source).filter(Boolean))
    ) as string[];

    return dataSources.map(source => ({ label: source, value: source }))
      .sort((a, b) => a.label.localeCompare(b.label));
  }, [tableData, activitySources]);

  return (
    <>
      <Row gutter={16} style={{ marginBottom: '20px' }}>
        <Col span={8}>
          <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>Search</div>
          <Input
            placeholder="Search activity by activity name..."
            value={searchValue}
            onChange={handleSearchInputChange}
            onPressEnter={handleSearchKeyPress}
            size="large"
            style={{ width: '100%' }}
            allowClear
          />
        </Col>
        <Col span={3}>
          <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>Geography</div>
          <Select
            mode="multiple"
            allowClear
            placeholder="Select geography"
            style={{ width: '100%' }}
            options={geographyOptions}
            value={geographyFilter}
            onChange={setGeographyFilter}
            maxTagCount="responsive"
            size="large"
            suffixIcon={<FilterOutlined />}
            dropdownStyle={{ maxHeight: '400px', overflow: 'auto' }}
            popupClassName="filter-dropdown"
            showArrow
          />
        </Col>
        <Col span={3}>
          <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>Unit</div>
          <Select
            mode="multiple"
            allowClear
            placeholder="Select unit"
            style={{ width: '100%' }}
            options={unitOptions}
            value={unitFilter}
            onChange={setUnitFilter}
            maxTagCount="responsive"
            size="large"
            suffixIcon={<FilterOutlined />}
            dropdownStyle={{ maxHeight: '400px', overflow: 'auto' }}
            popupClassName="filter-dropdown"
            showArrow
          />
        </Col>
        <Col span={3}>
          <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>Source</div>
          <Select
            mode="multiple"
            allowClear
            placeholder="Select source"
            style={{ width: '100%' }}
            options={sourceOptions}
            value={sourceFilter}
            onChange={setSourceFilter}
            maxTagCount="responsive"
            size="large"
            suffixIcon={<FilterOutlined />}
            dropdownStyle={{ maxHeight: '400px', overflow: 'auto' }}
            popupClassName="filter-dropdown"
            showArrow
          />
        </Col>
        <Col span={3}>
          <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>&nbsp;</div>
          <Button
            type="primary"
            icon={<SearchOutlined />}
            loading={isSearching}
            onClick={handleSearch}
            size="large"
            style={{ width: '100%' }}
          >
            Search
          </Button>
        </Col>
        <Col span={4}>
          <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>&nbsp;</div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedActivity(null);
              setShowCreateCustomEF(true);
            }}
            size="large"
            style={{ width: '100%' }}
          >
            Custom Activity
          </Button>
        </Col>
      </Row>

      <DataTable
        id={'emissions-factors-table'}
        key={tableKey}
        data={tableData}
        columns={columns}
        selectedItems={selectedFactors}
        filteredData={filteredData}
        refetch={handleRefetchEmissionsFactors}
        onRow={(record, index) => ({
          onClick: (event) => handleRowClick(record, index, event),
          className: 'clickable-row',
        })}
        onDataFiltered={(newFilteredData) => setFilteredData(newFilteredData)}
        tableLoading={refetchedDataIsLoading || isSearching}
        tableType="emissionsFactor"
      />

      {selectedActivity && (
        <>
          <IntermediateExchanges
            parentActivity={selectedActivity}
            isOpen={isIntermediateExchangesOpen}
            onClose={() => setIsIntermediateExchangesOpen(false)}
            onUpdate={() => handleRefetchEmissionsFactors()}
            orgName={tenantID}
            editMode={selectedActivity.source === tenantID.toUpperCase()}
          />
        </>
      )}

      {showCreateCustomEF && (
        <CreateCustomEmissionsFactor
          baseActivity={selectedActivity}
          isOpen={showCreateCustomEF}
          onClose={() => setShowCreateCustomEF(false)}
          onSuccess={(newEmissionsFactor) => {
            setShowCreateCustomEF(false);

            // If we got back a new emissions factor, set it as the selected activity
            // and open the intermediate exchanges modal
            if (newEmissionsFactor) {
              setSelectedActivity(newEmissionsFactor);
              setIsIntermediateExchangesOpen(true);
            }

            handleRefetchEmissionsFactors();
            notification.success({
              message: 'Custom Activity Created',
              description: `Successfully created custom activity`,
            });
          }}
          orgName={tenantID}
        />
      )}
    </>
  )
}
