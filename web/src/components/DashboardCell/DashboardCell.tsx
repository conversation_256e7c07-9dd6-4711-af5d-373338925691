import React, { useRef, useState } from 'react'
import type { DashboardMetricsQuery } from 'types/graphql'
import type { CellSuccessProps, CellFailureProps } from '@redwoodjs/web'
import {
  Card,
  Button,
  List,
  Progress,
  Avatar,
  Col,
  Row,
  Tag,
  Spin,
  Skeleton,
  Tooltip,
  Tour,
  Checkbox,
} from 'antd'
import type { TourProps } from 'antd'
import {
  Card as TremorCard,
  Metric,
  Text as TremorText,
  Bold,
} from '@tremor/react'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons'
import { Column } from '@ant-design/plots'
import { Link, navigate, routes } from '@redwoodjs/router'
import {
  ComposableMap,
  Geographies,
  Geography,
  ZoomableGroup,
  Marker,
} from 'react-simple-maps'
import { geoCentroid } from 'd3-geo'
import { useAuth, getOrgMemberInfo } from 'src/auth'
import './style.css'
import GettingStartedTour from '../GettingStartedTour/GettingStartedTour'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import { renderImpactFactorUnit, formatFloat } from 'src/utils/helper'
export const QUERY = gql`
  query DashboardMetricsQuery {
    getDashboardMetrics {
      totalProductEmissions
      averageProductFootprint
      totalProducts
      productCategories
      lifeCycleEmissionsByProductcategory
      lifeCycleEmissions {
        rawMaterialsEmissions
        packagingMaterialEmissions
        manufacturingEmissions
        distributionEmissions
        consumerUseEmissions
        eolEmissions
      }
      highestCarbonFootprintProducts {
        productName
        productId
        brand
        category
        imageUrl
        totalEmissions
      }
      lowestCarbonFootprintProducts {
        productName
        productId
        brand
        category
        imageUrl
        totalEmissions
      }
      productsByRegion
    }
  }
`

export const Loading = () => (
  <div>
    <Spin size="large" tip="Loading...">
      <Skeleton paragraph={{ rows: 30 }} active />
    </Spin>
  </div>
)

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const Success = (props: CellSuccessProps<DashboardMetricsQuery>) => {
  if (!props) {
    return <ErrorHandler />
  }

  const dashboardMetrics = props.getDashboardMetrics

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  let lifeCycleStagesMap = {
    rawMaterialsEmissions: 'Raw Materials',
    packagingMaterialEmissions: 'Packaging',
    manufacturingEmissions: 'Manufacturing',
    distributionEmissions: 'Distribution',
    consumerUseEmissions: 'Consumer Use',
    eolEmissions: 'End-of-Life',
  }

  const productCategoryEmissionsData = Object.keys(
    dashboardMetrics.lifeCycleEmissionsByProductcategory
  ).reduce((acc, category) => {
    const categoryData =
      dashboardMetrics.lifeCycleEmissionsByProductcategory[category]
    return acc.concat(
      Object.keys(categoryData).map((type) => ({
        category,
        type: lifeCycleStagesMap[type],
        value: formatFloat(categoryData[type] ?? 0, 2),
      }))
    )
  }, [])

  const colorPalette = {
    'Raw Materials': '#ffde1aff',
    Packaging: '#ffde1aff',
    Manufacturing: '#ffce00ff',
    Distribution: '#ff8d00ff',
    'Consumer Use': '#ff7400ff',
    'End-of-Life': '#ff7400ff',
  }

  const columnStackedChartConfig = {
    data: productCategoryEmissionsData,
    isStack: true,
    xField: 'category',
    yField: 'value',
    seriesField: 'type',
    autoFit: true,
    height: 485,
    theme: {
      colors10: [
        colorPalette['Raw Materials'],
        colorPalette['Packaging'],
        colorPalette['Manufacturing'],
        colorPalette['Distribution'],
        colorPalette['Consumer Use'],
        colorPalette['End-of-Life'],
      ],
    },
    interactions: [
      {
        type: 'active-region',
        enable: false,
      },
    ],
    connectedArea: {
      style: (oldStyle, element) => {
        return {
          fill: 'rgba(0,0,0,0.25)',
          stroke: oldStyle.fill,
          lineWidth: 0.5,
        }
      },
    },
  }

  const emissionsDataByLifeCycleStages = [
    {
      stage: 'Raw Materials',
      emissions: formatFloat(
        dashboardMetrics.lifeCycleEmissions.rawMaterialsEmissions ?? 0
      ),
      color: '#ffde1aff',
      icon: '/images/icons/ingredients.png',
    },
    {
      stage: 'Packaging',
      emissions: formatFloat(
        dashboardMetrics.lifeCycleEmissions.packagingMaterialEmissions ?? 0
      ),
      color: '#ffde1aff',
      icon: '/images/icons/packaging.png',
    },
    {
      stage: 'Manufacturing',
      emissions: formatFloat(
        dashboardMetrics.lifeCycleEmissions.manufacturingEmissions ?? 0
      ),
      color: '#ffce00ff',
      icon: '/images/icons/manufacturing.png',
    },
    {
      stage: 'Distribution',
      emissions: formatFloat(
        dashboardMetrics.lifeCycleEmissions.distributionEmissions ?? 0
      ),
      color: '#ff8d00ff',
      icon: '/images/icons/transport.png',
    },
    {
      stage: 'Consumer Use',
      emissions: formatFloat(
        dashboardMetrics.lifeCycleEmissions.consumerUseEmissions ?? 0
      ),
      color: '#ff7400ff',
      icon: '/images/icons/consumer_use.png',
    },
    {
      stage: 'End-of-Life',
      emissions: formatFloat(
        dashboardMetrics.lifeCycleEmissions.eolEmissions ?? 0
      ),
      color: '#ff7400ff',
      icon: '/images/icons/eol.png',
    },
  ]

  const lifeCycleActivityWithHighestEmissions = Object.keys(
    dashboardMetrics.lifeCycleEmissions
  ).reduce((a, b) =>
    dashboardMetrics.lifeCycleEmissions[a] >
    dashboardMetrics.lifeCycleEmissions[b]
      ? a
      : b
  )

  const maxZoomLevel = 4
  const [mapPosition, setMapPosition] = useState({ zoom: 1 })

  const handleZoomIn = () => {
    if (mapPosition.zoom >= maxZoomLevel) return
    setMapPosition((pos) => ({ ...pos, zoom: pos.zoom * 2 }))
  }

  const handleZoomOut = () => {
    if (mapPosition.zoom <= 1) return
    setMapPosition((pos) => ({ ...pos, zoom: pos.zoom / 2 }))
  }

  const handleMoveEnd = (mapPosition) => {
    setMapPosition(mapPosition)
  }

  const [isTourOpen, setIsTourOpen] = useState(
    !(userMetadata.user.metadata?.milestone_productTour ?? false)
  )

  const totalProductEmissionsRef = useRef(null)
  const avgProductFootprintRef = useRef(null)
  const totalNoSKURef = useRef(null)
  const categoryEmissionsChartRef = useRef(null)
  const lifeCycleEmissionsStagesRef = useRef(null)
  const highestFootprintRef = useRef(null)
  const lowestFootprintRef = useRef(null)

  return (
    <>
      <GettingStartedTour
        isOpen={isTourOpen}
        onClose={() => setIsTourOpen(false)}
      />
      <Row
        gutter={[16, 16]}
        style={{ marginTop: '15px', marginBottom: '15px' }}
      >
        <Col span={8}>
          <TremorCard
            ref={totalProductEmissionsRef}
            style={{ boxShadow: '2px 4px 8px 0px rgba(0, 0, 0, 0.2)' }}
          >
            <TremorText style={{ fontSize: '32px' }}>
              <Bold style={{ fontSize: '16px', color: 'black' }}>
                Total Product Emissions
              </Bold>
            </TremorText>
            <Metric style={{ marginTop: '10px' }}>
              {Math.round(
                dashboardMetrics.totalProductEmissions
              ).toLocaleString()}{' '}
              <Bold style={{ fontSize: '24px', color: 'lightgrey' }}>
                {renderImpactFactorUnit(userMetadata)}
              </Bold>
            </Metric>
          </TremorCard>
        </Col>
        <Col span={8}>
          <TremorCard
            ref={avgProductFootprintRef}
            style={{ boxShadow: '2px 4px 8px 0px rgba(0, 0, 0, 0.2)' }}
          >
            <TremorText>
              <Bold style={{ fontSize: '16px', color: 'black' }}>
                Average Product Footprint
              </Bold>
            </TremorText>
            <Metric style={{ marginTop: '10px' }}>
              {dashboardMetrics.averageProductFootprint.toFixed(2)}{' '}
              <Bold style={{ fontSize: '24px', color: 'lightgrey' }}>
                {renderImpactFactorUnit(userMetadata)}
              </Bold>
            </Metric>
          </TremorCard>
        </Col>
        <Col span={8}>
          <TremorCard
            ref={totalNoSKURef}
            style={{ boxShadow: '2px 4px 8px 0px rgba(0, 0, 0, 0.2)' }}
          >
            <TremorText>
              <Bold style={{ fontSize: '16px', color: 'black' }}>
                Total # of Product SKUs
              </Bold>
            </TremorText>
            <Metric style={{ marginTop: '10px' }}>
              {dashboardMetrics.totalProducts}{' '}
              <Bold style={{ fontSize: '24px', color: 'lightgrey' }}>
                SKU's
              </Bold>
            </Metric>
          </TremorCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card
            ref={categoryEmissionsChartRef}
            title={
              <span>
                Product Category Emissions{' '}
                <span style={{ color: 'lightgray' }}>({renderImpactFactorUnit(userMetadata)})</span>
              </span>
            }
          >
            <Column {...columnStackedChartConfig} />
          </Card>
        </Col>
        <Col span={8}>
          <Card
            ref={lifeCycleEmissionsStagesRef}
            title={
              <span>
                Emissions by Life Cycle Stage{' '}
                <span style={{ color: 'lightgray' }}>({renderImpactFactorUnit(userMetadata)})</span>
              </span>
            }
          >
            <List
              itemLayout="horizontal"
              bordered
              dataSource={emissionsDataByLifeCycleStages}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        style={{ border: 'none', display: 'revert' }}
                        src={
                          <img
                            src={item.icon}
                            style={{ marginTop: '10px', height: '30px' }}
                          ></img>
                        }
                      />
                    }
                    title={
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <div>{item.stage}</div>
                        <div>{item.emissions}</div>
                      </div>
                    }
                    description={
                      <div style={{ width: '100%', paddingRight: '16px' }}>
                        <Progress
                          strokeColor={item.color}
                          percent={parseFloat(
                            formatFloat(
                              (
                                (Number(item.emissions) /
                                Number(dashboardMetrics.lifeCycleEmissions[
                                  lifeCycleActivityWithHighestEmissions
                                ])) *
                                100
                              ) ?? 0
                            )
                          )}
                          showInfo={false}
                        />
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={[16, 16]} style={{ marginTop: '15px' }}>
        <Col span={8}>
          <Card
            ref={highestFootprintRef}
            title={
              <span>
                Highest Footprint{' '}
                <span style={{ color: 'lightgray' }}>({renderImpactFactorUnit(userMetadata)})</span>
              </span>
            }
          >
            <List
              itemLayout="horizontal"
              dataSource={dashboardMetrics.highestCarbonFootprintProducts}
              bordered
              renderItem={(product) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar src={product.imageUrl} />}
                    title={
                      <Link
                        style={{ fontSize: '14px' }}
                        to={routes.productDetail({
                          tenantID: tenantID,
                          productId: encodeURIComponent(product.productId),
                        })}
                      >
                        {product.productName}
                      </Link>
                    }
                  />
                  <Tag
                    color="default"
                    className="text-xs"
                    style={{
                      border: '2px solid orange',
                    }}
                  >
                    <p className="text-sm">
                      {product.totalEmissions.toFixed(2)}
                    </p>
                  </Tag>
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card
            ref={lowestFootprintRef}
            title={
              <span>
                Lowest Footprint{' '}
                <span style={{ color: 'lightgray' }}>({renderImpactFactorUnit(userMetadata)})</span>
              </span>
            }
          >
            <List
              itemLayout="horizontal"
              dataSource={dashboardMetrics.lowestCarbonFootprintProducts}
              bordered
              renderItem={(product) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar src={product.imageUrl} />}
                    title={
                      <Link
                        style={{ fontSize: '14px' }}
                        to={routes.productDetail({
                          tenantID: tenantID,
                          productId: encodeURIComponent(product.productId),
                        })}
                      >
                        {product.productName}
                      </Link>
                    }
                  />
                  <Tag
                    color="default"
                    style={{
                      border: '2px solid lightgreen',
                    }}
                  >
                    <p className="text-sm">
                      {product.totalEmissions.toFixed(2)}
                    </p>
                  </Tag>
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="Products by Region">
            <div
              style={{
                position: 'absolute',
                bottom: 20,
                left: 20,
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Button
                shape="circle"
                icon={<PlusOutlined />}
                onClick={handleZoomIn}
              />
              <Button
                shape="circle"
                icon={<MinusOutlined />}
                onClick={handleZoomOut}
                style={{ marginTop: '5px' }} // Add space between buttons
              />
            </div>
            <ComposableMap data-tip="" projectionConfig={{ scale: 200 }}>
              <ZoomableGroup zoom={mapPosition.zoom} onMoveEnd={handleMoveEnd}>
                <Geographies geography={'/lib/worldmap_countries_110m.json'}>
                  {({ geographies }) =>
                    geographies.map((geo, index) => {
                      const countryName = geo.properties.name
                        .replace(/\s+/g, '')
                        .toLowerCase()

                      return dashboardMetrics.productsByRegion[countryName] ? (
                        <React.Fragment key={geo.rsmKey}>
                          <Geography
                            geography={geo}
                            style={{
                              default: {
                                fill: '#D6D6DA',
                                outline: 'none',
                              },
                              hover: {
                                fill: 'rgb(255, 116, 0)',
                                outline: 'none',
                              },
                              pressed: {
                                fill: '#E42',
                                outline: 'none',
                              },
                            }}
                          />
                          <Tooltip
                            title={`${geo.properties.name} - ${dashboardMetrics.productsByRegion[countryName]}`}
                            placement="top"
                          >
                            <Marker coordinates={geoCentroid(geo)}>
                              <circle
                                r={8}
                                fill="rgb(255, 116, 0)"
                                stroke="#FFF"
                                strokeWidth={2}
                              />
                            </Marker>
                          </Tooltip>
                        </React.Fragment>
                      ) : (
                        <Geography
                          key={geo.rsmKey}
                          geography={geo}
                          style={{
                            default: {
                              fill: '#D6D6DA',
                              outline: 'none',
                            },
                            hover: {
                              fill: 'rgb(255, 116, 0)',
                              outline: 'none',
                            },
                            pressed: {
                              fill: '#E42',
                              outline: 'none',
                            },
                          }}
                        />
                      )
                    })
                  }
                </Geographies>
              </ZoomableGroup>
            </ComposableMap>
          </Card>
        </Col>
      </Row>
    </>
  )
}
