import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { Pie } from '@ant-design/plots';
import { renderImpactFactorUnit } from 'src/utils/helper';

const DonutChart = (userMetadata) => {
  const data = [
    { type: 'Raw Materials', value: 0.2475 },
    { type: 'Manufacturing', value: 0.561 },
    { type: 'Distribution', value: 0.2805 },
    { type: 'Consumer Use', value: 0.363 },
    { type: 'Disposal', value: 0.198 },
  ];
  const config = {
    appendPadding: 10,
    data,
    angleField: 'value',
    colorField: 'type',
    radius: 1,
    innerRadius: 0.6,
    label: {
      type: 'inner',
      offset: '-50%',
      content: '',
      style: {
        textAlign: 'center',
        fontSize: 14,
      },
    },
    statistic: {
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: `<p style="font-size: 18px;">3.45 ${renderImpactFactorUnit(userMetadata)}</p>`,
      },
    },
    // statistic: {
    //   title: false,
    //   content: {
    //     style: {
    //       display: 'none',
    //     },
    //   },
    // },
    color: ['#F9F298', '#F69828'],
    legend: {
      visible: false,
    },
  };
  return <Pie {...config} />;
};

export default DonutChart

