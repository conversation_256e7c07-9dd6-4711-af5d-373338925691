// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import Donut<PERSON>hart from './DonutChart'

const meta: Meta<typeof DonutChart> = {
  component: DonutChart,
}

export default meta

type Story = StoryObj<typeof DonutChart>

export const Primary: Story = {}
