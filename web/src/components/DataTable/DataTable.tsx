import React, { useEffect, useRef, useState } from 'react'
import {
  Table,
  Input,
  Button,
  notification,
  Dropdown,
  Space,
  Tooltip,
  Modal,
  Tag,
} from 'antd'
import {
  DownOutlined,
  SearchOutlined,
  PlusCircleOutlined,
  FileExcelOutlined,
  ExclamationCircleFilled,
} from '@ant-design/icons'
import { useMutation } from '@redwoodjs/web'
import { navigate, routes } from '@redwoodjs/router'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import UpgradePlan from '../UpgradePlan/UpgradePlan'
import { Resizable } from 'react-resizable'
import './style.css'

const { confirm } = Modal

const CLONE_PRODUCT_MUTATION = gql`
  mutation CloneProduct($productId: String!) {
    cloneProduct(productId: $productId) {
      productId
      productName
      clonedFromProductId
    }
  }
`

const DELETE_PRODUCT_MUTATION = gql`
  mutation DeleteProduct($productId: String!) {
    deleteProduct(productId: $productId)
  }
`

const ResizableTitle = (props) => {
  const { width, onResize, children, ...restProps } = props
  const ref = useRef<HTMLDivElement>(null)

  if (!width) {
    return <th {...restProps}>{children}</th>
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => e.stopPropagation()}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th ref={ref} {...restProps}>
        {children}
      </th>
    </Resizable>
  )
}

const DataTable = ({
  data,
  columns,
  title = null,
  showHeader = true,
  footer = null,
  style = {},
  searchable = false,
  filters = false,
  bordered = false,
  paginate = true,
  pageSize = 10,
  refetch = null,
  selectedItems = [],
  expandable = null,
  scroll = { x: 'max-content' },
  className = null,
  id = null,
  onRow = null,
  exportToCsv = null,
  onDataFiltered = null,
  tableLoading = false,
  tableType = '',
  addItems = [],
  filteredData = null,
}) => {
  const [searchText, setSearchText] = useState('')
  const [upgradePlanModalIsVisible, setUpgradePlanModalIsVisible] =
    useState(false)
  const [upgradePlanModalMessage, setUpgradePlanModalMessage] = useState(
    'This is a PRO feature. Please upgrade your plan to access all PRO features.'
  )
  const [_filteredData, setFilteredData] = useState(data)
  const [deleteProduct, { loading: deleteProductLoading }] =
    useMutation(DELETE_PRODUCT_MUTATION)
  const [cloneProduct, { loading: cloneProductLoading }] = useMutation(
    CLONE_PRODUCT_MUTATION
  )

  const [tableColumns, setTableColumns] = useState([])

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const effectiveFilteredData = filteredData || _filteredData;

  const handleSearch = (value: any) => {
    setSearchText(value)
    const filtered = data.filter((entry) =>
      Object.values(entry).some(
        (v) =>
          typeof v === 'string' && v.toLowerCase().includes(value.toLowerCase())
      )
    )
    setFilteredData(filtered)
    // Call onDataFiltered to update parent component's state
    onDataFiltered?.(filtered)
  }

  useEffect(() => {
    if (columns?.length) {
      const newCols = columns.map((col) => {
        if (!col.width) {
          return { ...col, width: 120 }
        }
        return { ...col }
      })
      setTableColumns(newCols)
    }
  }, [columns])

  const handleResize = (index) => (e, { size }) => {
    setTableColumns((prevColumns) => {
      const nextColumns = [...prevColumns]
      nextColumns[index] = {
        ...nextColumns[index],
        width: size.width,
      }
      return nextColumns
    })
  }

  const mergedColumns = tableColumns.map((col, index) => ({
    ...col,
    onHeaderCell: (column) => ({
      width: column.width,
      onResize: handleResize(index),
    }),
  }))

  const components = {
    header: {
      cell: ResizableTitle,
    },
  }

  const deleteSelectedItems = async () => {
    for (const item of selectedItems) {
      try {
        await deleteProduct({
          variables: { productId: item.productId },
        })

        notification.success({
          placement: 'topRight',
          message: `${tableType === 'product' ? 'Product' : 'Component'} Deleted`,
          description: `${tableType === 'product' ? 'Product' : 'Component'} ${
            item.productName
          } was deleted successfully`,
        })

        await refetch()
      } catch (error) {
        const errorMessage =
          error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
          error.message

        notification.error({
          placement: 'topRight',
          message: `Error deleting ${tableType}`,
          description: errorMessage,
        })
      }
    }
  }

  const showDeleteProductModal = () => {
    if (!selectedItems.length) return
    confirm({
      title: `Delete ${tableType === 'product' ? 'Product' : 'Component'}`,
      icon: <ExclamationCircleFilled />,
      content: (
      <div>
        Are you sure you want to delete these{' '}
        {tableType === 'product' ? 'products' : 'components'}?
        <ul>
        {selectedItems.map((x) => (
          <li key={x.productId}>{x.productName}</li>
        ))}
        </ul>
      </div>
      ),
      onOk: deleteSelectedItems,
      okText: 'Delete',
      okButtonProps: {
      id: `delete-${tableType}-modal-ok-button`,
      style: { backgroundColor: '#f3c314d4' },
      },
      onCancel() {},
    })
  }

  const cloneSelectedProduct = async () => {
    try {
      await cloneProduct({
        variables: { productId: selectedItems[0].productId },
      })

      notification.success({
        placement: 'topRight',
        message: 'Clone Successful',
        description: `Product ${selectedItems[0].productName} cloned successfully`,
      })

      return await refetch()
    } catch (error) {
      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message

      return notification.error({
        placement: 'topRight',
        message: 'Error cloning product',
        description: errorMessage,
      })
    }
  }

  const handleChange = function (pagination, filters, sorter) {
    let newFilteredData = [...data]

    //Process all filters
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key].length > 0) {
          newFilteredData = newFilteredData.filter(item => {
            if (key === 'tags' && (!item.tags || !Array.isArray(item.tags))) {
              return false;
            }

            if (key === 'tags') {
              return filters[key].every(tag => item.tags.includes(tag));
            } else {
              const itemValue = item[key];
              return filters[key].some(filterValue =>
                itemValue && itemValue.toString().toLowerCase() === filterValue.toString().toLowerCase()
              );
            }
          });
        }
      });
    }

    if (searchText) {
      newFilteredData = newFilteredData.filter((entry) =>
        Object.values(entry).some(
          (v) =>
            typeof v === 'string' &&
            v.toLowerCase().includes(searchText.toLowerCase())
        )
      )
    }

    if (sorter.field && sorter.order) {
      newFilteredData.sort((a, b) => {
        const valueA = a[sorter.field]
        const valueB = b[sorter.field]

        if (!isNaN(parseFloat(valueA)) && !isNaN(parseFloat(valueB))) {
          return sorter.order === 'ascend'
            ? parseFloat(valueA) - parseFloat(valueB)
            : parseFloat(valueB) - parseFloat(valueA)
        }

        return sorter.order === 'ascend'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA)
      })
    }

    setFilteredData(newFilteredData)
    onDataFiltered?.(newFilteredData)
  }

  const showUpgradePlanModal = (message) => {
    setUpgradePlanModalIsVisible(true)
    setUpgradePlanModalMessage(message)
  }

  const renderActionDropdown = () => {
    const allActions = getDefaultActionItems()
    if (!allActions.length) return null

    const items = allActions.map((item, index) => ({
      key: index.toString(),
      label: (
        <Tooltip title={item.tooltipText}>
          <Button
            style={{ color: 'black' }}
            type="text"
            loading={item.loading}
            onClick={item.onClick}
            disabled={item.disabled}
            id={item.id}
          >
            {item.label}
          </Button>
        </Tooltip>
      ),
    }))

    return (
      <Dropdown menu={{ items }}>
        <Button
          id='action-dropdown-button'
          style={{ marginLeft: '10px', backgroundColor: '#f3c314d4' }}
        >
          <Space>
            Action
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
    )
  }

  const isCloneButtonDisabled = selectedItems.length !== 1
  const isCompareButtonDisabled = !(selectedItems.length === 2)

  let isAddProductButtonEnabled = true

  if (id === 'products-table' && userMetadata.user.metadata?.isTrialUser) {
    const productCount = data.filter(
      (x) => x.clonedFromProductId === null
    ).length

    if (productCount >= (orgMemberInfo?.orgMetadata?.addProductCount ?? 1)) {
      isAddProductButtonEnabled = false
    }
  }

  const getDefaultActionItems = () => {
    const defaultActions = []

    if (tableType === 'product') {
      defaultActions.push({
        label: `Clone Selected product`,
        tooltipText: isCloneButtonDisabled
          ? `Please select exactly one product to clone`
          : '',
        loading: cloneProductLoading,
        onClick: cloneSelectedProduct,
        disabled: isCloneButtonDisabled,
        id: `clone-product-button`,
      })
    }

    if (tableType === 'product') {
      defaultActions.push({
        label: `Compare Selected product`,
        tooltipText:
          selectedItems.length !== 2
            ? `Please select exactly two products to compare.`
            : '',
        loading: false,
        onClick: () => {
          navigate(
            routes.compareProduct({
              productId: encodeURIComponent(selectedItems[0].productId),
              clonedProductId: encodeURIComponent(selectedItems[1].productId),
            })
          )
        },
        disabled: isCompareButtonDisabled,
        id: `compare-${tableType}-button`,
      })
    }

    defaultActions.push({
      label: `Delete Selected ${tableType}`,
      tooltipText:
        !selectedItems.length
          ? `Please select at least one ${tableType} to delete.`
          : '',
      loading: deleteProductLoading,
      onClick: showDeleteProductModal,
      disabled: !selectedItems.length,
      id: `delete-${tableType}-button`,
    })

    return defaultActions
  }

  const renderAddDropdown = () => {
    if (!addItems.length) return null

    const items = addItems.map((item, index) => ({
      key: index.toString(),
      label: (
        <Button
          style={{ color: 'black' }}
          type="link"
          loading={item.loading}
          disabled={item.disabled}
          onClick={() => {
            if (
              !isAddProductButtonEnabled &&
              userMetadata.user.metadata?.isTrialUser
            ) {
              showUpgradePlanModal(item.upgradeMessage)
            } else {
              item.onClick()
            }
          }}
          id={item.id}
        >
          {item.icon}
          {item.label} &nbsp;
          {!isAddProductButtonEnabled && item.isPro && (
            <Tag color="#f3c314d4">PRO</Tag>
          )}
        </Button>
      ),
    }))

    return (
      <Dropdown menu={{ items }}>
        <Button
          className="animate-pulse"
          style={{
            marginLeft: '10px',
            backgroundColor: '#f3c314d4',
            float: 'right',
          }}
          id={`add-${tableType}-dropdown-button`}
        >
          <Space>
            <PlusCircleOutlined />
            Add {tableType === 'product' ? 'Product' : 'Component'}
          </Space>
        </Button>
      </Dropdown>
    )
  }

  return searchable && filters ? (
    <div>
      <UpgradePlan
        visible={upgradePlanModalIsVisible}
        onClose={() => setUpgradePlanModalIsVisible(false)}
        message={upgradePlanModalMessage}
      />
      <Input
        style={{ width: '25%', marginBottom: '10px' }}
        placeholder="Search..."
        value={searchText}
        onChange={(e) => handleSearch(e.target.value)}
        prefix={<SearchOutlined />}
        id="search-input"
      />
      {renderActionDropdown()}
      {exportToCsv && (
        <Button
          style={{ marginLeft: 10 }}
          type="default"
          onClick={() => exportToCsv(_filteredData)}
        >
          <Space>
            Export
            <FileExcelOutlined />
          </Space>
        </Button>
      )}
      {renderAddDropdown()}
      <Table
        dataSource={effectiveFilteredData}
        columns={mergedColumns}
        components={components}
        title={title}
        showHeader={showHeader}
        footer={footer}
        bordered={bordered}
        style={style}
        expandable={expandable}
        pagination={
          paginate ? { pageSize: pageSize, showSizeChanger: false } : false
        }
        onChange={handleChange}
        className={className}
        id={id}
        onRow={onRow}
        loading={tableLoading}
      />
    </div>
  ) : searchable ? (
    <div>
      <Input
        style={{ width: '25%', marginBottom: '10px' }}
        placeholder="Search..."
        value={searchText}
        onChange={(e) => handleSearch(e.target.value)}
        prefix={<SearchOutlined />}
      />
      <Table
        dataSource={effectiveFilteredData}
        columns={mergedColumns}
        components={components}
        title={title}
        showHeader={showHeader}
        footer={footer}
        bordered={bordered}
        style={style}
        expandable={expandable}
        pagination={
          paginate ? { pageSize: pageSize, showSizeChanger: false } : false
        }
        onChange={handleChange}
        scroll={scroll}
        className={className}
        id={id}
        onRow={onRow}
        loading={tableLoading}
      />
    </div>
  ) : (
    <Table
    dataSource={effectiveFilteredData}
    columns={mergedColumns}
      title={title}
      showHeader={showHeader}
      footer={footer}
      bordered={bordered}
      style={style}
      expandable={expandable}
      pagination={
        paginate ? { pageSize: pageSize, showSizeChanger: false } : false
      }
      onChange={handleChange}
      scroll={scroll}
      className={className}
      components={components}
      id={id}
      onRow={onRow}
      loading={tableLoading}
    />
  )
}

export default DataTable