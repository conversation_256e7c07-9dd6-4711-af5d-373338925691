// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import CompareProducts from './CompareProducts'

const meta: Meta<typeof CompareProducts> = {
  component: CompareProducts,
}

export default meta

type Story = StoryObj<typeof CompareProducts>

export const Primary: Story = {}
