import React from 'react'
import {
  Spin,
  Skeleton,
  Layout,
  Card,
  Row,
  Col,
  Avatar,
  Divider,
  Progress,
  Popover,
} from 'antd'
import { ProgressBar } from '@tremor/react'
import { useQuery } from '@redwoodjs/web'
import { Content } from 'antd/es/layout/layout'
import DataTable from '../DataTable/DataTable'
import './style.css'
import { useAuth } from 'src/auth'
import { renderImpactFactorUnit } from 'src/utils/helper'
import { QUERY } from '../ProductInfoCell'

const CompareProducts = ({ productId, clonedProductId }) => {

  const {userMetadata} = useAuth()
  const { data: productData, loading: productInfoLoading } = useQuery(QUERY, {
    variables: { productId },
  })

  const { data: clonedProductData, loading: clonedProductInfoLoading } =
    useQuery(QUERY, {
      variables: { productId: clonedProductId },
    })

  if (productInfoLoading || clonedProductInfoLoading) {
    return (
      <div>
        <Spin size="large" tip="Loading...">
          <Skeleton paragraph={{ rows: 30 }} active />
        </Spin>
      </div>
    )
  }

  const getTotalEmissions = (productInfo) => {
    return (
      (productInfo.emissions?.materials?.totalEmissions ?? 0) +
      (productInfo.emissions?.packaging?.totalEmissions ?? 0) +
      (productInfo.emissions?.production?.totalEmissions ?? 0) +
      (productInfo.emissions?.transportation?.totalEmissions ?? 0) +
      (productInfo.emissions?.use?.totalEmissions ?? 0) +
      (productInfo.emissions?.eol?.totalEmissions ?? 0)
    )
  }

  const productInfo =
    getTotalEmissions(productData.getProductInfo) >
    getTotalEmissions(clonedProductData.getProductInfo)
      ? productData.getProductInfo
      : clonedProductData.getProductInfo

  const clonedProductInfo =
    getTotalEmissions(productData.getProductInfo) >
    getTotalEmissions(clonedProductData.getProductInfo)
      ? clonedProductData.getProductInfo
      : productData.getProductInfo

  const colors = {
    yellow: 'rgb(255, 222, 26)',
    orange: 'rgb(255, 116, 0)',
  }

  const emissionsByLifeCycleStagesColumn = [
    {
      title: 'Activity',
      dataIndex: 'activity',
    },
    {
      title: 'Percentage',
      dataIndex: 'percentage',
      render: (text) => `${parseFloat(text).toFixed(2)}%`,
    },
    {
      title: 'Emissions',
      dataIndex: 'emissions',
      render: (text) => `${parseFloat(text).toFixed(4)} ${renderImpactFactorUnit(userMetadata)}`,
      sorter: true,
    },
  ]

  const calculateEmissionsPercentage = (activityEmissions, totalEmissions) => {
    return parseFloat(((activityEmissions / totalEmissions) * 100).toFixed(2))
  }

  const createEmissionsData = (
    key,
    activity,
    emissions,
    totalEmissions,
    children = null,
    percentage = calculateEmissionsPercentage(emissions, totalEmissions)
  ) => ({
    key,
    activity,
    percentage,
    emissions,
    children,
  })

  const emissionsByLifeCycleStagesData = (_productInfo) => {
    const totalEmissions = getTotalEmissions(_productInfo)

    return [
      createEmissionsData(
        1,
        <b>Raw Materials</b>,
        _productInfo.emissions.materials?.totalEmissions ?? 0,
        totalEmissions,
        _productInfo.emissions.materials?.emissions.map((material, index) =>
          createEmissionsData(
            `1-${index}`,
            material.name,
            material.totalEmissions,
            totalEmissions
          )
        )
      ),
      createEmissionsData(
        2,
        <b>Packaging</b>,
        _productInfo.emissions.packaging?.totalEmissions ?? 0,
        totalEmissions,
        _productInfo.emissions.packaging?.emissions.map((packaging, index) =>
          createEmissionsData(
            `1-${index}`,
            packaging.name,
            packaging.totalEmissions,
            totalEmissions
          )
        )
      ),
      createEmissionsData(
        3,
        <b>Manufacturing</b>,
        _productInfo.emissions.production?.totalEmissions ?? 0,
        totalEmissions,
        _productInfo.emissions.production?.emissions.map((process, index) =>
          createEmissionsData(
            `2-${index}`,
            process.name,
            process.totalEmissions,
            totalEmissions
          )
        )
      ),
      createEmissionsData(
        4,
        <b>Distribution</b>,
        _productInfo.emissions.transportation?.totalEmissions ?? 0,
        totalEmissions,
        _productInfo.emissions.transportation?.emissions.map(
          (transport, index) =>
            createEmissionsData(
              `3-${index}`,
              transport.name,
              transport.totalEmissions,
              totalEmissions
            )
        )
      ),
      createEmissionsData(
        5,
        <b>Consumer Use</b>,
        _productInfo.emissions.use?.totalEmissions ?? 0,
        totalEmissions,
        _productInfo.emissions.use?.emissions.map((use, index) =>
          createEmissionsData(
            `4-${index}`,
            use.name,
            use.totalEmissions,
            totalEmissions
          )
        )
      ),
      createEmissionsData(
        6,
        <b>End of Life</b>,
        _productInfo.emissions.eol?.totalEmissions ?? 0,
        totalEmissions,
        _productInfo.emissions.eol?.emissions.map((eol, index) => {
          const disposalEmissions = [
            { type: 'Recycling', value: eol.recyclingEmissions },
            { type: 'Incineration', value: eol.incinerationEmissions },
            { type: 'Landfilling', value: eol.landfillingEmissions },
            { type: 'Composting', value: eol.compostingEmissions },
          ].filter((disposal) => disposal.value > 0)

          return createEmissionsData(
            `5-${index}`,
            `${eol.name} (${disposalEmissions.map((d) => d.type).join(', ')})`,
            eol.totalEmissions,
            totalEmissions
          )
        })
      ),
    ]
  }

  const lifeCycleEmissionsData = [
    {
      icon: '/images/icons/ingredients.png',
      activity: 'Raw Materials',
      product1Emissions: productInfo.emissions.materials?.totalEmissions ?? 0,
      product2Emissions: clonedProductInfo.emissions.materials?.totalEmissions ?? 0,
    },
    {
      icon: '/images/icons/packaging.png',
      activity: 'Packaging',
      product1Emissions: productInfo.emissions.packaging?.totalEmissions ?? 0,
      product2Emissions: clonedProductInfo.emissions.packaging?.totalEmissions ?? 0,
    },
    {
      icon: '/images/icons/manufacturing.png',
      activity: 'Manufacturing',
      product1Emissions: productInfo.emissions.production?.totalEmissions ?? 0,
      product2Emissions: clonedProductInfo.emissions.production?.totalEmissions ?? 0,
    },
    {
      icon: '/images/icons/transport.png',
      activity: 'Distribution',
      product1Emissions: productInfo.emissions.transportation?.totalEmissions ?? 0,
      product2Emissions:
        clonedProductInfo.emissions.transportation?.totalEmissions ?? 0,
    },
    {
      icon: '/images/icons/consumer_use.png',
      activity: 'Consumer Use',
      product1Emissions: productInfo.emissions.use?.totalEmissions ?? 0,
      product2Emissions: clonedProductInfo.emissions.use?.totalEmissions ?? 0,
    },
    {
      icon: '/images/icons/eol.png',
      activity: 'End of Life',
      product1Emissions: productInfo.emissions.eol?.totalEmissions ?? 0,
      product2Emissions: clonedProductInfo.emissions.eol?.totalEmissions ?? 0,
    },
  ]

  const product1MaxEmissions = Math.max(
    ...lifeCycleEmissionsData.map((x) => x.product1Emissions)
  )
  const product2MaxEmissions = Math.max(
    ...lifeCycleEmissionsData.map((x) => x.product2Emissions)
  )

  const calculateReductionsPercentage = (
    product1Emissions,
    product2Emissions,
    formatted = false
  ) => {
    const p1Emissions = Number(product1Emissions)
    const p2Emissions = Number(product2Emissions)

    if (isNaN(p1Emissions) || isNaN(p2Emissions)) {
      if (formatted) {
        return <p>Invalid Data</p>
      }
      return NaN
    }

    let percentage

    if (p2Emissions === 0) {
      if (p1Emissions === 0) {
        percentage = 0
      } else {
        percentage = -100
      }
    } else {
      percentage = 100 - (p1Emissions / p2Emissions) * 100
    }

    if (!formatted) return percentage

    if (percentage === -100) {
      return <p style={{ color: 'red' }}>-100%</p>
    }

    if (percentage === 0 || Math.abs(percentage) < 0.01) {
      return <p>0%</p>
    }

    return percentage < 0 ? (
      <p style={{ color: 'red' }}>-{Math.abs(percentage).toFixed(2)}%</p>
    ) : (
      <p style={{ color: 'green' }}>+{percentage.toFixed(2)}%</p>
    )
  }

  const product1TotalEmissions = getTotalEmissions(productInfo)
  const product2TotalEmissions = getTotalEmissions(clonedProductInfo)
  const maxProductEmissions = Math.max(
    product1TotalEmissions,
    product2TotalEmissions
  )

  const productEmissionsPercentage =
    (product1TotalEmissions / maxProductEmissions) * 100
  const clonedProductEmissionsPercentage =
    (product2TotalEmissions / maxProductEmissions) * 100
  const productEmissionsReductionPercent =
    ((maxProductEmissions -
      Math.min(product1TotalEmissions, product2TotalEmissions)) /
      maxProductEmissions) *
    100

  const lifeCycleEmissionsColumns = [
    {
      title: 'Activity',
      dataIndex: 'activity',
      width: '20%',
      render: (text) => <p style={{ fontWeight: 'bold' }}>{text}</p>,
    },
    {
      title: 'Graph Product 1',
      className: 'product1-emissions-graph',
      width: '30%',
      render: (text, record) => (
        <>
          {text ? (
            <Popover
              content={
                <>
                  <p style={{ fontWeight: 'bold' }}>
                    {productInfo.productName}
                  </p>
                  <p>{record.product1Emissions?.toFixed(8)} {renderImpactFactorUnit(userMetadata)}</p>
                  <p style={{ fontWeight: 'bold' }}>
                    {clonedProductInfo.productName}
                  </p>
                  <p>{record.product2Emissions?.toFixed(8)} {renderImpactFactorUnit(userMetadata)}</p>
                </>
              }
              title={record.activity}
            >
              <Progress
                style={{ transform: 'rotateY(180deg)' }}
                strokeLinecap="butt"
                showInfo={false}
                success={{
                  percent:
                    (record.product1Emissions / product1MaxEmissions) * 100,
                  strokeColor: colors.orange,
                }}
                percent={
                  record.product1Emissions < record.product2Emissions
                    ? (record.product1Emissions / product1MaxEmissions) * 100 +
                      (record.product2Emissions / product1MaxEmissions) *
                        100 *
                        (calculateReductionsPercentage(
                          record.product1Emissions,
                          record.product2Emissions
                        ) /
                          100)
                    : (record.product1Emissions / product1MaxEmissions) * 100
                }
                size={25}
                strokeColor={'rgb(195 189 189)'}
                trailColor="transparent"
              />
            </Popover>
          ) : (
            <p
              style={{
                position: 'absolute',
                right: '40%',
                top: '12px',
                fontWeight: 'bold',
              }}
            >
              {record.product1Name}
            </p>
          )}
        </>
      ),
    },
    {
      title: 'Graph Product 2',
      width: '30%',
      className: 'product2-emissions-graph',
      render: (text, record) => (
        <div>
          <div
            style={{
              borderLeft: '1px solid grey',
              height: '100%',
              position: 'absolute',
              left: 0,
            }}
          ></div>
          {text ? (
            <Popover
              content={
                <>
                  <p style={{ fontWeight: 'bold' }}>
                    {productInfo.productName}
                  </p>
                  <p>{record.product1Emissions?.toFixed(8)} {renderImpactFactorUnit(userMetadata)}</p>
                  <p style={{ fontWeight: 'bold' }}>
                    {clonedProductInfo.productName}
                  </p>
                  <p>{record.product2Emissions?.toFixed(8)} {renderImpactFactorUnit(userMetadata)}</p>
                </>
              }
              title={record.activity}
            >
              <Progress
                strokeLinecap="butt"
                showInfo={false}
                success={{
                  percent:
                    (record.product2Emissions / product1MaxEmissions) * 100,
                  strokeColor: colors.yellow,
                }}
                percent={
                  record.product2Emissions < record.product1Emissions
                    ? (record.product2Emissions / product1MaxEmissions) * 100 +
                      (record.product1Emissions / product1MaxEmissions) *
                        100 *
                        (calculateReductionsPercentage(
                          record.product2Emissions,
                          record.product1Emissions
                        ) /
                          100)
                    : (record.product2Emissions / product1MaxEmissions) * 100
                }
                size={25}
                strokeColor={'rgb(195 189 189)'}
                trailColor="transparent"
              />
            </Popover>
          ) : (
            <p
              style={{
                position: 'absolute',
                left: '5%',
                top: '12px',
                fontWeight: 'bold',
              }}
            >
              {record.product2Name}
            </p>
          )}
        </div>
      ),
    },
    {
      title: 'Percentage',
      width: '20%',
      render: (text, record) => (
        <>
          {text && (
            <p style={{ fontWeight: 'bold' }}>
              {calculateReductionsPercentage(
                record.product2Emissions,
                record.product1Emissions,
                true
              )}
            </p>
          )}
        </>
      ),
    },
  ]

  return (
    <>
      <Layout>
        <Content style={{ backgroundColor: 'transparent', display: 'flex' }}>
          <Card
            title={
              <Row>
                <Col flex="auto">
                  <p style={{ marginTop: '5px', fontSize: '16px' }}>
                    {productInfo.productName}
                  </p>
                </Col>
                <Col flex="0">
                  <Avatar
                    shape="square"
                    style={{
                      backgroundColor:
                        product1TotalEmissions > product2TotalEmissions
                          ? colors.orange
                          : colors.yellow,
                    }}
                    size="default"
                  />
                </Col>
              </Row>
            }
            style={{ flex: '0 0 50%' }}
          >
            <DataTable
              showHeader={false}
              columns={emissionsByLifeCycleStagesColumn}
              expandable={{ indentSize: 20 }}
              paginate={false}
              data={emissionsByLifeCycleStagesData(productInfo)}
            />
            <div style={{ marginTop: '40%' }}>
              <Row>
                <Col style={{ marginLeft: '15px' }} flex="auto">
                  <p style={{ fontSize: '18px' }}>Carbon Footprint (GWP):</p>
                </Col>
                <Col style={{ marginRight: '15px' }}>
                  <p style={{ fontSize: '18px' }}>
                    <b>{product1TotalEmissions.toFixed(2)}</b> {renderImpactFactorUnit(userMetadata)}
                  </p>
                </Col>
              </Row>
            </div>
          </Card>
          <Card
            title={
              <Row>
                <Col flex="auto">
                  <p style={{ marginTop: '5px', fontSize: '16px' }}>
                    {clonedProductInfo.productName}
                  </p>
                </Col>
                <Col flex="0">
                  <Avatar
                    shape="square"
                    style={{
                      backgroundColor:
                        product1TotalEmissions < product2TotalEmissions
                          ? colors.orange
                          : colors.yellow,
                    }}
                    size="default"
                  />
                </Col>
              </Row>
            }
            style={{ flex: '0 0 50%' }}
          >
            <DataTable
              showHeader={false}
              columns={emissionsByLifeCycleStagesColumn}
              expandable={{ indentSize: 20 }}
              paginate={false}
              data={emissionsByLifeCycleStagesData(clonedProductInfo)}
            />
            <div style={{ marginTop: '40%' }}>
              <Row>
                <Col style={{ marginLeft: '15px' }} flex="auto">
                  <p style={{ fontSize: '18px' }}>Carbon Footprint (GWP):</p>
                </Col>
                <Col style={{ marginRight: '15px' }}>
                  <p style={{ fontSize: '18px' }}>
                    <b>{product2TotalEmissions.toFixed(2)}</b> {renderImpactFactorUnit(userMetadata)}
                  </p>
                </Col>
              </Row>
            </div>
          </Card>
        </Content>
        <Content>
          <Card
            style={{ marginTop: '15px' }}
            title="Product Comparison: Results Summary"
          >
            <div style={{ position: 'relative', padding: '10px' }}>
              <div style={{ marginBottom: '20px' }}>
                <b>{productInfo.productName}</b>
                <ProgressBar
                  value={productEmissionsPercentage}
                  style={{
                    height: '10px',
                    marginTop: '10px',
                    marginBottom: '10px',
                  }}
                  color={
                    product1TotalEmissions > product2TotalEmissions
                      ? 'orange'
                      : 'yellow'
                  }
                />
                <p>
                  <b>{product1TotalEmissions.toFixed(2)}</b> {renderImpactFactorUnit(userMetadata)}
                </p>
              </div>

              <div style={{ marginTop: '30px', marginBottom: '20px' }}>
                <b>{clonedProductInfo.productName}</b>
                <ProgressBar
                  value={clonedProductEmissionsPercentage}
                  style={{
                    height: '20px',
                    marginTop: '10px',
                    marginBottom: '5px',
                  }}
                  color={
                    product1TotalEmissions < product2TotalEmissions
                      ? 'orange'
                      : 'yellow'
                  }
                />
                <p>
                  <b>{product2TotalEmissions.toFixed(2)}</b> {renderImpactFactorUnit(userMetadata)}
                </p>
              </div>

              <div
                style={{
                  position: 'absolute',
                  top: '0',
                  bottom: '0',
                  left: `${Math.min(
                    productEmissionsPercentage,
                    clonedProductEmissionsPercentage
                  )}%`,
                  borderLeft: '2px dotted lightgrey',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                }}
              >
                <div
                  style={{
                    transform: 'translate(-50%, -50%)',
                    backgroundColor: 'white',
                    padding: '2px 5px',
                    fontSize: '0.8rem',
                    fontWeight: 'bold',
                  }}
                >
                  {productEmissionsReductionPercent.toFixed(2)}% Reduction
                </div>
              </div>
            </div>
          </Card>
          <Card
            style={{ marginTop: '15px' }}
            title="Product Comparison: Life Cycle Stages"
          >
            <DataTable
              showHeader={false}
              bordered={false}
              paginate={false}
              columns={lifeCycleEmissionsColumns}
              data={lifeCycleEmissionsData}
              className="no-border-table"
            />
            <Divider />
            <div>
              <Row
                align="middle"
                justify="space-between"
                style={{ marginBottom: '8px' }}
              >
                <Col span={6} style={{ textAlign: 'left' }}>
                  <p style={{ marginBottom: 0, fontSize: '16px' }}>Total:</p>
                </Col>
                <Col span={4} style={{ textAlign: 'left' }}>
                  <p style={{ marginBottom: 0, fontSize: '16px' }}>
                    <b>{product1TotalEmissions.toFixed(2)}</b> {renderImpactFactorUnit(userMetadata)}
                  </p>
                </Col>
                <Col span={10} style={{ textAlign: 'left' }}>
                  <p style={{ marginBottom: 0, fontSize: '16px' }}>
                    <b>{product2TotalEmissions.toFixed(2)}</b> {renderImpactFactorUnit(userMetadata)}
                  </p>
                </Col>
              </Row>
            </div>
          </Card>
        </Content>
      </Layout>
    </>
  )
}

export default CompareProducts
