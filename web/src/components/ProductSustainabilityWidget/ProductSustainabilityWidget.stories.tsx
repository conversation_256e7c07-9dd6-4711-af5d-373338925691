// Pass props to your component by passing an `args` object to your story
//
// ```tsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import ProductSustainabilityWidget from './ProductSustainabilityWidget'

const meta: Meta<typeof ProductSustainabilityWidget> = {
  component: ProductSustainabilityWidget,
  tags: ['autodocs'],
}

export default meta

type Story = StoryObj<typeof ProductSustainabilityWidget>

export const Primary: Story = {}
