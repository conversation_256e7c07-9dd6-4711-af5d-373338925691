import { useState, useEffect } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import {
  Button,
  Drawer,
  Form,
  Input,
  Space,
  notification,
  Spin,
  Tag,
  Row,
  Col,
  Divider,
  Checkbox,
  Select,
} from 'antd'
import { SaveOutlined } from '@ant-design/icons'
import { CREATE_EMISSIONS_FACTOR_MUTATION, EF_INTERMEDIATE_EXCHANGES_QUERY } from 'src/utils/graphql'
import countries from 'src/components/AddProduct/countries'

const CreateCustomEmissionsFactor = ({
  baseActivity,
  isOpen,
  onClose,
  onSuccess,
  orgName,
}) => {
  const [loading, setLoading] = useState(false)
  const [intermediateExchanges, setIntermediateExchanges] = useState([])
  const [selectedExchanges, setSelectedExchanges] = useState([])
  const [form] = Form.useForm()

  const [createEmissionsFactor] = useMutation(CREATE_EMISSIONS_FACTOR_MUTATION)
  const [getIntermediateExchanges, { loading: exchangesLoading }] = useLazyQuery(
    EF_INTERMEDIATE_EXCHANGES_QUERY,
    {
      onCompleted: (data) => {
        if (data?.getEFIntermediateExchanges) {
          const exchanges = data.getEFIntermediateExchanges;
          setIntermediateExchanges(exchanges);
          setSelectedExchanges(exchanges.map(exchange => exchange.id));
        }
      },
      onError: (error) => {
        notification.error({
          message: 'Error fetching datasets',
          description: error.message,
        })
      },
    }
  )

  const getAvailableEFUnits = () => {
    return [
      { label: 'Kilograms (kg)', value: 'kg' },
      { label: 'Square Meters (m2)', value: 'm2' },
      { label: 'Unit (unit)', value: 'unit' },
      { label: 'Meters (m)', value: 'm' },
      { label: 'Cubic Meters (m3)', value: 'm3' },
      { label: 'Megajoules (MJ)', value: 'MJ' },
      { label: 'Hectares (ha)', value: 'ha' },
      { label: 'Kilowatt Hours (kWh)', value: 'kWh' },
      { label: 'Guest Nights (guest night)', value: 'guest night' },
      { label: 'Square Meters per Year (m*year)', value: 'm*year' },
      { label: 'Hours (hour)', value: 'hour' },
      { label: 'Kilometers (km)', value: 'km' },
      { label: 'Liters (l)', value: 'l' },
      { label: 'Square Meters per Year (m2*year)', value: 'm2*year' },
      { label: 'Kilometers per Year (km*year)', value: 'km*year' },
      { label: 'Metric Tonne-Kilometers (metric ton*km)', value: 'metric ton*km' },
      { label: 'Kilograms per Day (kg*day)', value: 'kg*day' },
      { label: 'Person-Kilometers (person*km)', value: 'person*km' },
    ]
  }

  useEffect(() => {
    if (isOpen) {
      if (baseActivity) {
        // Clone existing activity
        form.setFieldsValue({
          activityName: baseActivity.activityName,
          description: baseActivity.description || `Cloned from ${baseActivity.activityName}`,
          referenceProduct: baseActivity.referenceProduct,
          geography: baseActivity.geography,
          source: orgName.toUpperCase(),
          unit: baseActivity.unit,
        })

        getIntermediateExchanges({
          variables: {
            activityName: baseActivity.activityName,
            referenceProduct: baseActivity.referenceProduct,
            geography: baseActivity.geography,
            source: baseActivity.source,
            sharedScope: baseActivity.source == orgName.toUpperCase() ? false : true,
          },
        })
      } else {
        // Create new custom activity
        form.setFieldsValue({
          activityName: '',
          description: '',
          referenceProduct: '',
          geography: 'GLO',
          source: orgName.toUpperCase(),
          unit: 'kg',
        })

        // Clear exchanges for new activities
        setIntermediateExchanges([]);
        setSelectedExchanges([]);
      }
    }
  }, [baseActivity, isOpen, form, getIntermediateExchanges, orgName])

  const handleSubmit = async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()

      const exchangesToClone = intermediateExchanges.filter(exchange =>
        selectedExchanges.includes(exchange.id)
      );

      const formattedExchanges = exchangesToClone.map(exchange => ({
        exchange_name: exchange.exchangeName,
        amount: exchange.amount,
        unit: exchange.unit,
        input_stream: parseFloat(exchange.amount) > 0 ? true : false,
        exchange_emissions_factor: {
          activity_name: exchange.exchangeEmissionsFactor.activityName,
          activity_type: 'materials',
          reference_product_name: exchange.exchangeEmissionsFactor.referenceProduct,
          geography: exchange.exchangeEmissionsFactor.geography,
          source: exchange.exchangeEmissionsFactor.source,
          unit: exchange.exchangeEmissionsFactor.unit,
        },
      }));

      const emissionsFactorInput = {
        parent_emissions_factor: {
          activity_name: values.activityName,
          activity_type: "materials",
          activity_description: values.description,
          unit: values.unit,
          reference_product_name: values.referenceProduct,
          geography: values.geography,
          source: values.source,
        },
        exchanges: formattedExchanges,
        elemental_ef_values: []
      }

      const { data } = await createEmissionsFactor({
        variables: {
          emissionsFactor: emissionsFactorInput,
        },
      })

      if (data?.createEmissionsFactor) {
        setLoading(false)

        // Create a properly formatted emissions factor object to pass back to parent
        const newEmissionsFactor = {
          efId: data.createEmissionsFactor.id,
          activityName: values.activityName,
          description: values.description,
          referenceProduct: values.referenceProduct,
          geography: values.geography,
          source: values.source,
          unit: values.unit,
          isTenant: true
        }

        // Pass the newly created emissions factor to parent component
        onSuccess(newEmissionsFactor)
      }
    } catch (error) {
      setLoading(false)
      notification.error({
        message: 'Error creating custom emissions factor',
        description: error.message,
      })
    }
  }

  return (
    <Drawer
      title={baseActivity ? "Clone Activity" : "Create Custom Activity"}
      open={isOpen}
      onClose={onClose}
      width={700}
      extra={
        <Space>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={handleSubmit}
          >
            Save
          </Button>
        </Space>
      }
    >
      <Spin spinning={loading || exchangesLoading}>
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="activityName"
                label="Activity Name"
                rules={[{ required: true, message: 'Please enter activity name' }]}
              >
                <Input placeholder="Enter activity name" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="description"
                label="Description"
              >
                <Input.TextArea rows={3} placeholder="Enter description" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="referenceProduct"
                label="Reference Product"
                rules={[{ required: true, message: 'Please enter reference product' }]}
              >
                <Input placeholder="Enter reference product" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="geography"
                label="Geography"
                rules={[{ required: true, message: 'Please select geography' }]}
              >
                <Select
                  disabled={baseActivity}
                  showSearch
                  placeholder="Select geography"
                  options={countries}
                  filterOption={(input, option) =>
                    option.value.toLowerCase().includes(input.toLowerCase()) ||
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="source"
                label="Source"
                rules={[{ required: true, message: 'Please enter source' }]}
              >
                <Input placeholder="Enter source" disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="Unit"
                rules={[{ required: true, message: 'Please select unit' }]}
              >
                <Select
                  disabled={baseActivity}
                  showSearch
                  placeholder="Select unit"
                  options={getAvailableEFUnits()}
                  filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
          </Row>

          {baseActivity && (
            <>
              <Divider>Dataset Details</Divider>

              {intermediateExchanges.length > 0 ? (
                <div>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Tag color="blue">{intermediateExchanges.length} exchanges</Tag>
                    </div>
                  </div>

                  {intermediateExchanges.map((exchange, index) => (
                    <div
                      key={index}
                      style={{
                        marginBottom: '10px',
                        padding: '10px',
                        border: '1px solid #f0f0f0',
                        borderRadius: '4px',
                        backgroundColor: selectedExchanges.includes(exchange.id) ? '#f6ffed' : 'white'
                      }}
                    >
                      <Row align="middle">
                        <Col span={15}>
                          <strong>{exchange.exchangeName}</strong>
                        </Col>
                        <Col span={8}>
                          <Tag color="blue">{exchange.amount} {exchange.unit}</Tag>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={23}>
                          <small>
                            {exchange.exchangeEmissionsFactor.activityName} ({exchange.exchangeEmissionsFactor.geography}, {exchange.exchangeEmissionsFactor.source})
                          </small>
                        </Col>
                      </Row>
                    </div>
                  ))}
                </div>
              ) : (
                <p>No datasets found for this emissions factor.</p>
              )}
            </>
          )}

        </Form>
      </Spin>
    </Drawer>
  )
}

export default CreateCustomEmissionsFactor
