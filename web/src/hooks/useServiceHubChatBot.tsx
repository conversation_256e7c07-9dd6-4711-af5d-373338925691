import { useEffect, useState, useRef } from 'react'

declare global {
  interface Window {
    hsConversationsOnReady?: (() => void)[]
    HubSpotConversations: {
      widget: {
        open: () => void
        close: () => void
        load: () => void
        remove: () => void
      }
      on: (event: string, callback: (payload: any) => void) => void
    }
  }
}

export const useServiceHubChatBot = (portalId) => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [hasLoaded, setHasLoaded] = useState<boolean>(false)
  const [activeConversation, setActiveConversation] = useState<boolean>(false)
  const eventRef = useRef(null)

  useEffect(() => {
    if (isOpen) {
      window.hsConversationsOnReady = [
        () => {
          setHasLoaded(true)
        },
      ]

      let script = document.createElement('script')
      script.src = `//js.hs-scripts.com/${portalId}.js`
      script.async = true
      document.body.appendChild(script)

      return () => {
        window.HubSpotConversations.widget.close()
        document.body.removeChild(script)
        window.hsConversationsOnReady = []
      }
    }
  }, [isOpen, portalId])

  useEffect(() => {
    if (hasLoaded) {
      if (isOpen) {
        window.HubSpotConversations.widget.open()
      }

      eventRef.current = (payload) => {
        setActiveConversation(payload.conversation.conversationId)
      }

      window.HubSpotConversations.on('conversationStarted', eventRef.current)
      window.HubSpotConversations.on('widgetClosed', () => {
        setIsOpen(false)
        setHasLoaded(false)
        window.HubSpotConversations.widget.remove()
      })
    }
  }, [hasLoaded, setHasLoaded, isOpen, setIsOpen])

  const openHandler = () => {
    setIsOpen(true)

    window.HubSpotConversations.widget.load()
    window.HubSpotConversations.widget.open()
  }

  return {
    hasLoaded,
    activeConversation,
    isOpen,
    openHandler,
  }
}
