import { useEffect, useRef } from 'react'

const handleBeforeUnload = (e: BeforeUnloadEvent, condition: boolean) => {
  if (condition) {
    e.preventDefault()
    e.returnValue = ''
  }
}

export const useUnsavedChangeWarning = (isUnsaved: boolean) => {
  const isConfirmingRef = useRef(false)

  useEffect(() => {
    const beforeUnloadHandler = (e: BeforeUnloadEvent) => {
      handleBeforeUnload(e, isUnsaved)
    }

    const popStateHandler = (e: PopStateEvent) => {
      if (!isUnsaved) return

      if (isConfirmingRef.current) {
        isConfirmingRef.current = false
        return
      }

      const confirmLeave = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this page?'
      )

      if (confirmLeave) {
        isConfirmingRef.current = true
      } else {
        // Prevent navigation by pushing the current state back
        e.preventDefault(); // Prevent the default popstate behavior
        history.pushState(null, '', window.location.href); // Push the current state to keep the user on the same page
      }
    }

    const handleLinkClick = (e: MouseEvent) => {
      if (!isUnsaved) return

      const target = e.target as HTMLElement
      const link = target.closest('a')

      if (!link || !link.href || link.target === '_blank' || link.getAttribute('download')) return

      const isSameOrigin = link.origin === window.location.origin
      if (!isSameOrigin) return

      e.preventDefault()
      e.stopPropagation()

      const confirmLeave = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this page?'
      )

      if (confirmLeave) {
        isConfirmingRef.current = true
        window.location.assign(link.href)
      }
    }

    // Init event listeners
    window.addEventListener('beforeunload', beforeUnloadHandler)
    window.addEventListener('popstate', popStateHandler)
    document.addEventListener('click', handleLinkClick, true)

    history.pushState(null, '', window.location.href)

    // Remove event listeners on unmount
    return () => {
      window.removeEventListener('beforeunload', beforeUnloadHandler)
      window.removeEventListener('popstate', popStateHandler)
      document.removeEventListener('click', handleLinkClick, true)
    }
  }, [isUnsaved])
}
