import { getOrgMemberInfo } from "src/auth";

export const renderImpactFactorUnit = (userMetadata) => {
  const orgMemberInfo = getOrgMemberInfo(userMetadata)
  const tenantID =
  orgMemberInfo && orgMemberInfo.urlSafeOrgName
    ? orgMemberInfo.urlSafeOrgName
    : null
  return userMetadata?.user?.metadata?.defaultImpactFactor?.[tenantID]?.unit ?? 'kg CO2e';
}

export const formatFloat = (val, decimals = 2) => {
  const rounded = parseFloat(val).toFixed(decimals)

  if (parseFloat(rounded) === 0 && val !== 0) {
    return Number(parseFloat(val).toPrecision(decimals))
  }

  if (Math.abs(val) < Math.pow(10, -decimals)) {
    return Number(parseFloat(val).toPrecision(decimals))
  }

  return Number(rounded)
}

export const toScientificNotation = (num, sigDigits = 3) => {
  // Convert to exponential notation with specified significant digits
  const expNotation = num.toExponential(sigDigits - 1);
  return expNotation;
}