import { useSentry } from '@envelop/sentry'
import { createGraphQLHandler } from '@redwoodjs/graphql-server'

import directives from 'src/directives/**/*.{js,ts}'
import sdls from 'src/graphql/**/*.sdl.{js,ts}'
import services from 'src/services/**/*.{js,ts}'

import { authDecoder, getCurrentUser } from 'src/lib/auth'
import { db } from 'src/lib/db'
import { logger } from 'src/lib/logger'
import Sentry from 'src/lib/sentry'

export const handler = createGraphQLHandler({
  extraPlugins: [
    useSentry({
      includeRawResult: true,
      includeResolverArgs: true,
      includeExecuteVariables: true,
    }),
  ],
  authDecoder,
  getCurrentUser,
  loggerConfig: { logger },
  directives,
  sdls,
  services,
  onException: (error, context) => {
    logger.error('GraphQL execution exception', {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
      },
      operationName: context?.request?.operationName,
      query: context?.request?.query,
      variables: context?.request?.variables,
      requestId: context?.request?.context?.requestId,
    })

    Sentry.captureException(error, {
      extra: {
        operationName: context?.request?.operationName,
        graphQLQuery: context?.request?.query,
      },
    })

    db.$disconnect()
  },
})
