import type { APIGatewayEvent, Context } from 'aws-lambda'
import { useRequireAuth, context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import { getCurrentUser, isAuthenticated, authDecoder } from 'src/lib/auth'
import { logger } from 'src/lib/logger'
import axios from 'axios'

/**
 * The handler function is your code that processes http request events.
 * You can use return and throw to send a response or error, respectively.
 *
 * Important: When deployed, a custom serverless function is an open API endpoint and
 * is your responsibility to secure appropriately.
 *
 * @see {@link https://redwoodjs.com/docs/serverless-functions#security-considerations|Serverless Function Considerations}
 * in the RedwoodJS documentation for more information.
 *
 * @typedef { import('aws-lambda').APIGatewayEvent } APIGatewayEvent
 * @typedef { import('aws-lambda').Context } Context
 * @param { APIGatewayEvent } event - an object which contains information from the invoker.
 * @param { Context } context - contains information about the invocation,
 * function, and execution environment.
 */
const downloadLcaReport = async (event: APIGatewayEvent, _context: Context) => {
  logger.info(`${event.httpMethod} ${event.path}: downloadLcaReport function`)

  try {
    const { productId } = event.queryStringParameters

    if (isAuthenticated()) {
      const { orgName: tenantID } = context.currentUser.orgMemberInfo;

      const response = await axios.post(
        `${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${productId}/download-lca-report`,
        {},
        {
          responseType: 'arraybuffer',
        }
      )

      if (response.status === 200) {
        const buffer = Buffer.from(response.data)
        return {
          statusCode: 200,
          body: buffer.toString('base64'),
          headers: {
            'content-Type': response.headers['content-type'],
          },
          isBase64Encoded: true,
        }
      }
    } else {
      return {
        statusCode: 401,
        error: 'Unauthorized',
      }
    }

  } catch (error) {
    logger.error('Error downloading LCA report:', error)
    return {
      statusCode: 500,
      error: error,
    }
  }
}

export const handler = useRequireAuth({
  handlerFn: downloadLcaReport,
  getCurrentUser,
  authDecoder,
})