export const schema = gql`
  type ProductAttributes {
    productId: String!
    key: String!
    value: String!
  }

  type Product {
    productName: String!
    productId: String!
    category: String
    brand: String
    imageUrl: String
    materialEmissions: Float
    packagingMaterialEmissions: Float
    manufacturingEmissions: Float
    distributionEmissions: Float
    consumerUseEmissions: Float
    eolEmissions: Float
    totalEmissions: Float
    clonedFromProductId: String
    annualSalesVolumeUnits: Int
    tags: [String]
    attributes: [ProductAttributes!]
    createdAt: String
    updatedAt: String
  }

  type Query {
    getProducts(isComponent: Boolean): [Product!]! @requireAuth
  }
`
