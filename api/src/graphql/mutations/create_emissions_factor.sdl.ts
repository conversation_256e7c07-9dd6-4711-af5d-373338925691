export const schema = gql`
  type CreatedEmissionsFactor {
    activityName: String
    activityType: String
    description: String
    referenceProduct: String
    geography: String
    source: String
    kgCO2e: Float
    unit: String
  }

  input CreateParentEmissionsFactorInput {
    activity_name: String!
    activity_type: String!
    unit: String!
    activity_description: String
    reference_product_name: String!
    geography: String!
    source: String!
    kg_co2e: Float
  }

  input CreateEFIntermediateExchangeInput {
    exchange_name: String!
    amount: Float!
    unit: String!
    input_stream: Boolean!
    exchange_emissions_factor: CreateParentEmissionsFactorInput!
  }

  input CreateElementalEfValueInput {
    lcia_method: String!
    impact_category_name: String!
    impact_category_indicator: String!
    impact_category_unit: String!
    amount: Float!
  }

  input CreateEmissionsFactorInput {
    parent_emissions_factor: CreateParentEmissionsFactorInput!
    exchanges: [CreateEFIntermediateExchangeInput]
    elemental_ef_values: [CreateElementalEfValueInput]
  }

  type Mutation {
    createEmissionsFactor(
      emissionsFactor: CreateEmissionsFactorInput!
    ): CreatedEmissionsFactor! @requireAuth
  }
`
