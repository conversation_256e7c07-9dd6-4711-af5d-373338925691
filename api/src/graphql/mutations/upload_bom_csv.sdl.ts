export const schema = gql`
  type CsvColumnMapping {
    userColumn: String!
    systemField: String
  }

  type BomMaterial {
    raw_material: String!
    weight: MaterialWeight!
    description: String
    component: String
    cas_number: String
    supplier_name: String
    supplier_location: SupplierLocation
  }

  type MaterialWeight {
    amount: Float!
    unit: String!
  }

  type SupplierLocation {
    city: String
    country: String
  }

  type UploadBomCsvResponse {
    success: Boolean!
    message: String
    materials: [BomMaterial]
    warnings: [String]
    processedRows: Int
    totalRows: Int
    unmappedColumns: [String]
  }

  type Mutation {
    uploadBomCsv(
      base64Data: String!
      contentType: String!
    ): UploadBomCsvResponse @requireAuth
  }
`