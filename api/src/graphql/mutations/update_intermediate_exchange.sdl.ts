export const schema = gql`
  type UpdatedIntermediateExchange {
    id: Int!
    exchangeName: String!
    amount: Float!
    unit: String!
  }

  input IntermediateExchangeEmissionsFactorInput {
    activity_name: String!
    activity_type: String!
    reference_product_name: String!
    geography: String!
    source: String!
    unit: String!
  }

  input IntermediateExchangeInput {
    exchange_name: String!
    amount: Float!
    unit: String!
    exchange_emissions_factor: IntermediateExchangeEmissionsFactorInput!
  }

  type Mutation {
    updateIntermediateExchange(
      exchangeId: Int!
      intermediateExchange: IntermediateExchangeInput!
    ): UpdatedIntermediateExchange! @requireAuth
  }
`
