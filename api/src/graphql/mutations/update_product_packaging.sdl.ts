export const schema = gql`
  type ProductPackagingResponse {
    material: String!
    component: String!
    packagingType: String!
    weight: Float!
  }
  input ProductPackagingInput {
    packaging_material: String!
    packaging_item: String!
    packaging_level: String!
    weight_grams: Float!
    country_of_origin: String
    city_of_origin: String
    country_code: String
    supplier_name: String
    supplier_origin_latitude: Float
    supplier_origin_longitude: Float
    recycled_content: Float
  }
  type Mutation {
    updateProductPackaging(
      packagingId: Int!
      productPackaging: ProductPackagingInput!
    ): ProductPackagingResponse! @requireAuth
  }
`
