export const schema = gql`
  type Ingredients {
    ingredientName: String!
    weight: Float!
  }

  input SupplierAddressInput {
    country: String!
    state_or_province: String
    city: String
    address_1: String
    latitude: Float
    longitude: Float
  }

  input SupplierInput {
    supplier_name: String!
    primary_address: SupplierAddressInput
  }

  input IngredientEmissionsFactorInput {
    activity_name: String!
    activity_type: String!
    reference_product: String!
    geography: String!
    source: String!
    exchanges: [ExchangeInput]
  }

  input ActivityInput {
    activity_name: String!
    reference_product_name: String!
    product_information: String!
    geography: String!
    source: String!
  }

  input ExchangeInput {
    exchange_name: String!
    amount: Float!
    unit: String!
    input_stream: Boolean!
    exchange_emissions_factor: ActivityInput
  }

  input CreateProductIngredientsInput {
    ingredient_name: String!
    weight: Float!
    unit: String!
    proprietary_name: String
    cas_number: String
    wastage_rate: Int
    supplier: SupplierInput
    emissions_factor: IngredientEmissionsFactorInput
    exchanges: [ExchangeInput]
    recycled_content: Float
  }

  type Mutation {
    createProductIngredients(
      productId: String!
      productIngredients: [CreateProductIngredientsInput!]!
    ): [Ingredients] @requireAuth
  }
`
