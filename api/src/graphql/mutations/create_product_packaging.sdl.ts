export const schema = gql`
  type ProductPackaging {
    packagingMaterial: String!
    packagingItem: String!
    weight: Float!
    packagingLevel: String!
  }

  input CreateProductPackagingInput {
    packaging_material: String!
    packaging_level: String!
    packaging_item: String!
    weight_grams: Float!
    country_of_origin: String
    city_of_origin: String
    origin_country_code: String
    supplier_origin_latitude: Float
    supplier_origin_longitude: Float
    country_code: String
    supplier_name: String
    recycled_content: Float
  }

  type Mutation {
    createProductPackaging(
      productId: String!
      productPackaging: CreateProductPackagingInput!
    ): ProductPackaging! @requireAuth
  }
`
