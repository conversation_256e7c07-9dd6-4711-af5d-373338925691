export const schema = gql`
  input UpdateNodeEmissionsFactorInput {
    activity_name: String!
    geography: String!
    reference_product_name: String!
    source: String!
  }

  input UpdateNodeInput {
    id: Int!
    name: String!
    amount: Float
    unit: String
    component: String
    description: String
    quantity: Float
    emissions_factor: UpdateNodeEmissionsFactorInput!
  }

  type Mutation {
    updateNodes(
      productId: String!
      updateNodePayload: [UpdateNodeInput]!
    ): String @requireAuth
  }
`
