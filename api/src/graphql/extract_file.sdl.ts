export const schema = gql`
  type ExtractFileProcessModelEdge {
    fromNodeId: Int
    toNodeId: Int
  }

  type ExtractFileProcessModelLocation {
    city: String
    country: String
  }

  type ExtractFileProcessModelNode {
    id: Int
    name: String
    component: String
    description: String
    packagingLevel: String
    nodeType: String
    location: ExtractFileProcessModelLocation
    amount: Float
    quantity: Float
    unit: String
    scrapRate: Float
    scrapFate: String
  }

  type ExtractFileComponent {
    componentId: String
    quantity: Float
  }

  type ProductInfoResponse {
    productName: String
    productID: String
    annualSalesVolume: String
    factoryCity: String
    factoryCountry: String
    nodes: [ExtractFileProcessModelNode]
    edges: [ExtractFileProcessModelEdge]
    components: [ExtractFileComponent]
  }
  type Query {
    extractFile(base64Data: String!, contentType: String!): ProductInfoResponse
      @requireAuth
  }
`
