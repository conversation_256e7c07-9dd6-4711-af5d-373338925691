export const schema = gql`
  type Activity {
    activityUUID: String
    activityName: String!
    referenceProduct: String
    productInformation: String!
    similarity: Float
    curated: Boolean
    geography: String
    source: String
    unit: String
  }

  type ExchangeActivity {
    activityName: String!
    referenceProduct: String!
    geography: String!
    source: String!
    unit: String
  }

  type Exchange {
    exchangeName: String!
    amount: Float!
    unit: String!
    inputStream: Boolean!
    exchangeEmissionsFactor: ExchangeActivity!
  }

  type ElementalEfValue {
    lciaMethod: String!
    impactCategoryName: String!
    impactCategoryIndicator: String!
    impactCategoryUnit: String!
    amount: Float!
  }

  type ActivityWithExchanges {
    activityUUID: String
    activityName: String!
    referenceProduct: String
    productInformation: String!
    kgCO2e: Float
    similarity: Float
    curated: Boolean
    geography: String
    source: String
    unit: String
    exchanges: [Exchange]
    modified: Boolean
    elementalEfValues: [ElementalEfValue]
  }

  type ActivityRecommendations {
    matchedActivity: ActivityWithExchanges
    confidence: String
    explanation: String
    recommendations: [Activity]
  }
  type Query {
    predictEmissionsFactors(
      chemicalName: String!
      productCategory: String
      casNo: String
      geography: String
      geographyModeling: Boolean
      unit: String
      labs: Boolean
    ): ActivityRecommendations @requireAuth
  }
`
