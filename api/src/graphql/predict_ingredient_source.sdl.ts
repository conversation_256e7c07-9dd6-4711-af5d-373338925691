export const schema = gql`
  type IngredientSource {
    ingredientName: String!
    hscode: String!
    productCategory: String
    manufacturingCountry: String
    countryCode: String
    country: String
    countryCapital: String
    latitude: Float
    longitude: Float
    locallyProcured: Boolean
  }

  type Query {
    predictIngredientSource(
      ingredientName: String!
      country: String!
    ): IngredientSource @requireAuth
  }
`