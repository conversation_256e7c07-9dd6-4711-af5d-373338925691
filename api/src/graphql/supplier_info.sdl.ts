export const schema = gql`
  type RawMaterialEmissions {
    ingredientName: String!
    totalEmissions: Float!
    ingredientEmissions: Float!
  }

  type PackagingEmissions {
    packageMaterial: String!
    totalEmissions: Float!
  }

  type IngredientDistributionEmissions {
    ingredientName: String!
    totalEmissions: Float!
    transportEmissions: Float!
  }

  type PackagingDistributionEmissions {
    packageMaterial: String!
    totalEmissions: Float!
    transportEmissions: Float!
  }

  type Product {
    productName: String!
    productId: String!
    category: String!
    brand: String!
    annualSalesVolumeUnits: Int!
    rawMaterialEmissions: [RawMaterialEmissions]
    packagingEmissions: [PackagingEmissions]
    ingredientDistributionEmissions: [IngredientDistributionEmissions]
    packagingDistributionEmissions: [PackagingDistributionEmissions]
    totalEmissions: Float
    scope3emissions: Float
  }

  type Supplier {
    supplierName: String
    supplierId: Int!
    supplierLevel: Int!
    supplierType: String
    taxRegistryNumber: String
    primaryContactName: String
    website: String
    stateOrProvince: String
    postalCode: String
    country: String
    city: String
    latitude: Float
    longitude: Float
    primaryAddress1: String
    primaryAddress2: String
    totalProducts: Int
    products: [Product]
    scope3emissions: Float
  }

  type Query {
    getSupplierInfo(supplierId: String!): Supplier @requireAuth
  }
`
