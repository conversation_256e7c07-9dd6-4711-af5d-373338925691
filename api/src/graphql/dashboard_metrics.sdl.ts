export const schema = gql`
  type Product {
    productName: String!
    productId: String!
    category: String!
    brand: String!
    imageUrl: String
    totalEmissions: Float
  }

  type LifeCycleEmissions {
    rawMaterialsEmissions: Float!
    packagingMaterialEmissions: Float!
    manufacturingEmissions: Float!
    distributionEmissions: Float!
    consumerUseEmissions: Float!
    eolEmissions: Float!
  }

  type DashboardMetrics {
    totalProductEmissions: Float!
    averageProductFootprint: Float!
    totalProducts: Int!
    productCategories: [String]!
    lifeCycleEmissionsByProductcategory: JSON
    lifeCycleEmissions: LifeCycleEmissions
    highestCarbonFootprintProducts: [Product]
    lowestCarbonFootprintProducts: [Product]
    productsByRegion: JSON
  }

  type Query {
    getDashboardMetrics: DashboardMetrics @requireAuth
  }
`
