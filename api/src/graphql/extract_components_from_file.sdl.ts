export const schema = gql`
  type ExtractFileProcessModelEdge {
    fromNodeId: Int
    toNodeId: Int
  }

  type ExtractFileProcessModelLocation {
    city: String
    country: String
  }

  type ExtractFileProcessModelNode {
    id: Int
    name: String
    component: String
    description: String
    nodeType: String
    location: ExtractFileProcessModelLocation
    amount: Float
    quantity: Float
    unit: String
    scrapRate: Float
    scrapFate: String
  }

  type ComponentsResponse {
    componentId: String
    componentName: String
    nodes: [ExtractFileProcessModelNode]
    edges: [ExtractFileProcessModelEdge]
  }

  type ExtractComponentsFromFileResponse {
    components: [ComponentsResponse]
    warnings: [String]
  }

  type Query {
    extractComponentsFromFile(
      base64Data: String!
      contentType: String!
    ): ExtractComponentsFromFileResponse @requireAuth
  }
`
