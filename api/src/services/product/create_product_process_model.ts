import { context } from '@redwoodjs/graphql-server'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createProductProcessModel = async ({
  productId,
  processModel,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.post(
      `${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${sanitizeInput(
        productId
      )}/process-model`,
      sanitizeInput(processModel)
    )

    return true
  } catch (error) {
    let errorMessage = 'Error creating product process model. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
