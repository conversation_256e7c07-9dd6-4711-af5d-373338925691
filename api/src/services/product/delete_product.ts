import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const deleteProduct = async ({ productId }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.delete(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${sanitizeInput(productId)}`
    )

    return true

  } catch (error) {
    let errorMessage = 'Error deleting product. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
