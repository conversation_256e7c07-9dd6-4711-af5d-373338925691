import { context } from '@redwoodjs/graphql-server'
import { GraphQLError } from 'graphql'
import axios from 'axios'
import { DEFAULT_IMPACT_FACTOR, sanitizeInput } from 'src/lib/helper'

export const getSuppliers = async () => {
  try {
    const { urlSafeOrgName: tenantID } = context.currentUser.orgMemberInfo

    const impactFactor = context.currentUser.metadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR

    // Itarate product list first
    const productsResponse = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}`
    )
    const products = productsResponse.data.products

    if (!Array.isArray(products)) {
      throw new Error('Unexpected response format from the server.')
    }

    const activeSupplierMap = new Map()

    const getGWPImpact = (segment) => {
      const gwpImpact = segment.impacts?.find(
        (impact) =>
          impact.emissions_factor_value?.impact_indicator?.lcia_method ===
            impactFactor.lciaMethod &&
          impact.emissions_factor_value?.impact_indicator?.category ===
            impactFactor.categoryName &&
          impact.emissions_factor_value?.impact_indicator?.indicator ===
            impactFactor.indicator
      )
      return gwpImpact?.impact_amount ?? 0
    }

    await Promise.all(
      products.map(async (product) => {
        try {
          const processModel = await axios.get(
            `${
              process.env.LCA_API_ENDPOINT
            }/v2/products/${tenantID}/${sanitizeInput(
              product.product_id
            )}/process-model`
          )

          const walkerData = await axios.get(
            `${
              process.env.LCA_API_ENDPOINT
            }/v2/products/${tenantID}/${sanitizeInput(
              product.product_id
            )}/process-model/walk`
          )

          const walkerEmissions = walkerData?.data['default']

          const productNodes = processModel.data.nodes
          const processModelEdges = processModel.data.edges
          const materialSegments = walkerEmissions.materials?.segment_nodes ?? []
          const packagingSegments = walkerEmissions.packaging?.segment_nodes ?? []
          const transportSegments =
            walkerEmissions.transportation?.segment_nodes ?? []

          const materialNodes = productNodes.filter(
            (node) =>
              node.supplier?.id &&
              (node.node_type === 'material' || node.node_type === 'packaging')
          )

          materialNodes.forEach((node) => {
            const supplierId = node.supplier.id

            if (!activeSupplierMap.has(supplierId)) {
              activeSupplierMap.set(supplierId, {
                supplierName: `${node.name} Supplier for ${product.product_name}`,
                supplierId: supplierId,
                supplierLevel: node.supplier.supplier_level,
                supplierType: node.supplier.supplier_type,
                country: node.location?.country,
                city: node.location?.city,
                address: '',
                products: [],
                scope3Emissions: 0,
                materials: new Set([node.name]),
              })
            } else {
              // Add material to existing supplier's inventory
              activeSupplierMap.get(supplierId).materials.add(node.name)
            }

            const materialSegment = [
              ...materialSegments,
              ...packagingSegments,
            ].find((segment) => segment.node_id === node.id)

            const materialNodeEdge = processModelEdges.find(
              (edge) => edge.from_node_id === node.id
            )

            const transportNode = productNodes.find(
              (node) =>
                node.id === materialNodeEdge.to_node_id &&
                node.node_type === 'transportation'
            )

            const transportSegment = transportSegments.find(
              (segment) => segment.node_id === transportNode.id
            )

            const materialNodeEmissions = materialSegment
              ? [
                  {
                    materialName: node.name,
                    totalEmissions: getGWPImpact(materialSegment),
                    amount: materialSegment.amount,
                    unit: materialSegment.unit,
                  },
                ]
              : []

            const transportNodeEmissions = transportSegment
              ? [
                  {
                    materialName: transportNode.name,
                    totalEmissions: getGWPImpact(transportSegment),
                    amount: transportSegment.amount,
                    unit: transportSegment.unit,
                  },
                ]
              : []

            const totalEmissions = [
              ...(materialSegment ? [getGWPImpact(materialSegment)] : []),
              ...(transportSegment ? [getGWPImpact(transportSegment)] : []),
            ].reduce((sum, emission) => sum + emission, 0)

            const productData = {
              productId: product.product_id,
              productName: product.product_name,
              brand: product.brand,
              category: product.category,
              annualSalesVolumeUnits: product.annual_sales_volume_units || 1,
              materialNodeEmissions,
              transportNodeEmissions,
              totalEmissions,
              scope3Emissions: totalEmissions,
            }

            const supplier = activeSupplierMap.get(supplierId)
            if (
              !supplier.products.some((p) => p.productId === product.product_id)
            ) {
              supplier.products.push(productData)
              supplier.scope3Emissions += productData.scope3Emissions
            }
          })
        } catch (error) {
          console.error(
            `Error processing product ${product.product_id}:`,
            error
          )
        }
      })
    )

    return Array.from(activeSupplierMap.values())
      .map((supplier) => {
        const { materials, ...supplierData } = supplier
        return {
          ...supplierData,
          totalProducts: supplier.products.length,
        }
      })
      .filter((supplier) => supplier.products.length > 0)
  } catch (error) {
    console.error('Error fetching suppliers:', error)
    let errorMessage = 'Error fetching suppliers. Please try again'

    if (error.response?.status === 503) {
      errorMessage =
        'Our application is starting up. Please try again in a few minutes.'
    }

    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    }

    throw new GraphQLError(errorMessage)
  }
}
