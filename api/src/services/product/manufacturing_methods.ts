import axios from 'axios'
import { GraphQLError } from 'graphql'

export const getManufacturingMethods = async () => {
  try {
    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/manufacturing-processes/`
    )

    return response.data.map((manufacturingMethod) => {
      return {
        processName: manufacturingMethod.process_name,
        geographyIso3: manufacturingMethod.geography_iso3,
        electricityKwh: manufacturingMethod.electricity_kwh,
        waterLiters: manufacturingMethod.water_liters,
        co2eKg: manufacturingMethod.co2e_kg,
        amountOfProductKg: manufacturingMethod.amount_of_product_kg,
      }
    })
  } catch (error) {
    let errorMessage = 'Error fetching manufacturing methods. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
