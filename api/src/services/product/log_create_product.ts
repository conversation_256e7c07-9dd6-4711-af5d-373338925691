import { context } from '@redwoodjs/graphql-server'
import { UpdateUserMetadata } from 'src/lib/auth'
import { GraphQLError } from 'graphql'

export const logCreateProduct = async () => {
  try {
    const response = await UpdateUserMetadata(
      context.currentUser.userId,
      'milestone_createProduct',
      true
    )

    return response
  } catch (error) {
    console.error(error)
    let errorMessage = 'Error updating milestone_createProduct.'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
