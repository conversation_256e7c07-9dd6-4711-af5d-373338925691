import axios from 'axios'
import { context } from '@redwoodjs/graphql-server'
import { base64ToBlob, sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'
import Sentry from 'src/lib/sentry'

function capitalize(str) {
  if (typeof str !== 'string' || str.length === 0) {
    return str
  }
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export const extractFile = async ({ base64Data, contentType }) => {
  try {

    const blob = base64ToBlob(base64Data, contentType)

    const formData = new FormData()
    formData.append('file', blob, 'product_info')

    let headers = {
      'Content-Type': 'multipart/form-data',
      ...(context.currentUser.orgMemberInfo.orgMetadata
        ?.processModelEnabled && {
        'X-Use-Graph-Extraction': 'true',
        'X-Parts-Only': 'true',
      }),
    }

    const response = await axios.post(
      `${process.env.ML_MODELS_ENDPOINT}/api/file-extraction/`,
      formData,
      {
        headers,
      }
    )

    const nodes = response.data.product?.nodes ?? response.data.nodes
    const edges = response.data.product?.edges ?? response.data.edges
    const components = Array.isArray(response.data) && response.data.length ? response.data : []

    return {
      productName: response.data.product?.product_name,
      productID: response.data.product?.product_id,
      annualSalesVolume: response.data.product?.annual_sales_volume,
      factoryCity: response.data.product?.factory?.city,
      factoryCountry: response.data.product?.factory?.country,
      nodes: nodes
        ? nodes.map((node) => {
            return {
              id: node.node_id,
              name: node.name,
              component: node.component_name,
              description: node.description,
              packagingLevel: node.packaging_level,
              nodeType: node.node_type,
              location: {
                city: node.location?.city,
                country: node.location?.country,
              },
              amount: node.amount,
              quantity: node.quantity,
              unit: node.unit,
              scrapRate: node.scrap_rate ?? 0,
              scrapFate: node.scrap_fate,
            }
          })
        : [],
      edges: edges
        ? edges.map((edge) => {
            return {
              fromNodeId: edge.from_node_id,
              toNodeId: edge.to_node_id,
            }
          })
        : [],
      components: components.map((component) => {
        return {
          componentId: component.component_id,
          quantity: component.quantity,
        }
      }),
    }
  } catch (error) {
    console.log(error)
    let errorMessage = 'Error reading file. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    Sentry.captureException(error)
    throw new GraphQLError(errorMessage)
  }
}
