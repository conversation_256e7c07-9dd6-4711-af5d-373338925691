import axios from 'axios'
import { context } from '@redwoodjs/graphql-server'
import { retryOnFail } from 'src/lib/http-request'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'
import { UpdateUserMetadata } from 'src/lib/auth'

export const predictEmissionsFactors = async ({
  chemicalName,
  productCategory,
  casNo,
  geography,
  geographyModeling,
  unit,
  labs = false,
}) => {
  const retryCount = 3

  if (
    !context.currentUser.orgMemberInfo ||
    !Object.keys(context.currentUser.orgMemberInfo).length
  ) {
    if (!context.currentUser.metadata?.test_user) {
      if (
        (context.currentUser.metadata?.quota_labs_ef_matching_used ?? 0) >=
        parseInt(context.currentUser.metadata?.quota_labs_ef_matching ?? 20)
      ) {
        throw new GraphQLError(
          'You have exceeded your quota for emissions factor matching requests. Please contact support to upgrade your plan.'
        )
      }
    }
  }

  let _headers = {
    'api-version': 'latest',
  }

  if (!labs) {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo
    _headers['X-Tenant-ID'] = tenantID
  }

  const response = await retryOnFail(retryCount, () =>
    axios.post(
      `${process.env.LCA_API_ENDPOINT}/raw-materials/activities/recommendations`,
      {
        chemical_name: sanitizeInput(chemicalName),
        product_category: productCategory
          ? sanitizeInput(productCategory)
          : null,
        cas_number: sanitizeInput(casNo),
        geography: sanitizeInput(geography),
        geography_modeling: geographyModeling,
        unit: unit,
      },
      {
        headers: _headers,
      }
    )
  )

  if (!response) {
    throw new GraphQLError(
      `Failed to get predicted emissions factor match for '${chemicalName}'`
    )
  }

  UpdateUserMetadata(
    context.currentUser.userId,
    'quota_labs_ef_matching_used',
    (context.currentUser.metadata?.quota_labs_ef_matching_used ?? 0) + 1
  )

  return {
    matchedActivity: {
      activityUUID: response.data.matched_activity.activity_uuid,
      activityName: response.data.matched_activity.activity_name,
      referenceProduct: response.data.matched_activity.reference_product_name,
      productInformation: response.data.matched_activity.product_information,
      similarity: response.data.matched_activity.similarity,
      curated: response.data.matched_activity.curated,
      geography: response.data.matched_activity.geography,
      source: response.data.matched_activity.source,
      unit: response.data.matched_activity.unit,
      kgCO2e: response.data.matched_activity.kg_co2e,
      exchanges: response.data.matched_activity.exchanges.map((exchange) => ({
        exchangeName: exchange.exchange_name,
        amount: exchange.amount,
        unit: exchange.unit,
        inputStream: exchange.input_stream,
        exchangeEmissionsFactor: {
          activityName: exchange.exchange_emissions_factor.activity_name,
          referenceProduct:
            exchange.exchange_emissions_factor.reference_product_name,
          geography: exchange.exchange_emissions_factor.geography,
          source: exchange.exchange_emissions_factor.source,
          unit: exchange.exchange_emissions_factor.unit ?? exchange.unit,
        },
      })),
      modified: response.data.matched_activity.modified,
      elementalEfValues: response.data.matched_activity.elemental_ef_values?.map((ef) => ({
        lciaMethod: ef.lcia_method,
        impactCategoryName: ef.impact_category_name,
        impactCategoryIndicator: ef.impact_category_indicator,
        impactCategoryUnit: ef.impact_category_unit,
        amount: ef.amount,
      })),
    },
    confidence: response.data.confidence,
    explanation: response.data.explanation,
    recommendations: response.data.recommendations.map((activity) => ({
      activityUUID: activity.activity_uuid,
      activityName: activity.activity_name,
      referenceProduct: activity.reference_product_name,
      productInformation: activity.product_information,
      similarity: activity.similarity,
      curated: activity.curated,
      geography: activity.geography,
      source: activity.source,
      unit: activity.unit,
    })),
  }
}
