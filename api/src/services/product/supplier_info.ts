import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { DEFAULT_IMPACT_FACTOR, sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const getSupplierInfo = async ({ supplierId }) => {
  try {
    const { urlSafeOrgName: tenantID } = context.currentUser.orgMemberInfo

    const impactFactor = context.currentUser.metadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR

    const [supplierResponse, productResponse] = await Promise.all([
      axios.get(
        `${process.env.LCA_API_ENDPOINT}/${tenantID}/suppliers/${sanitizeInput(
          supplierId
        )}`
      ),
      axios.get(`${process.env.LCA_API_ENDPOINT}/products/${tenantID}`),
    ])

    const supplierInfo = supplierResponse.data
    const productData = productResponse.data.products

    if (!Array.isArray(productData)) {
      throw new Error('Unexpected response format from the server.')
    }

    const getWalkerData = async (productId) => {
      try {
        const walkerResponse = await axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(
            productId
          )}/process-model/walk`
        )
        return walkerResponse.data['default']
      } catch (error) {
        console.error(
          `Error fetching walker data for product ${productId}:`,
          error
        )
        return null
      }
    }

    const getProcessModel = async (productId) => {
      try {
        const processModelResponse = await axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(productId)}/process-model`
        )
        return processModelResponse.data
      } catch (error) {
        console.error(
          `Error fetching process model for product ${productId}:`,
          error
        )
        return null
      }
    }

    const createProductObject = (
      product,
      materialEmissions,
      manufacturingEmissions,
      transportationEmissions,
      totalEmissions
    ) => {
      return {
        productId: product.product_id,
        productName: product.product_name,
        brand: product.brand,
        category: product.category,
        annualSalesVolumeUnits: product.annual_sales_volume_units || 1,
        materialEmissions,
        manufacturingEmissions,
        transportationEmissions,
        totalEmissions,
        scope3emissions: totalEmissions,
      }
    }

    const getGWPImpact = (segment) => {
      const gwpImpact = segment.impacts?.find(
        (impact) =>
          impact.emissions_factor_value?.impact_indicator?.lcia_method ===
            impactFactor.lciaMethod &&
          impact.emissions_factor_value?.impact_indicator?.category ===
            impactFactor.categoryName &&
          impact.emissions_factor_value?.impact_indicator?.indicator ===
            impactFactor.indicator
      )
      return {
        totalEmissions: gwpImpact?.impact_amount ?? 0,
        emissionsFactor:
          gwpImpact?.emissions_factor_value?.emissions_factor ?? null,
      }
    }

    const getSupplierProducts = async () => {
      const productsWithEmissions = await Promise.all(
        productData.map(async (product) => {
          const [walkerData, processModel] = await Promise.all([
            getWalkerData(product.product_id),
            getProcessModel(product.product_id),
          ])

          if (!walkerData || !processModel) {
            return null
          }

          const supplierNodes = processModel.nodes.filter(
            (node) => node.supplier?.id == supplierId
          )

          if (!supplierNodes.length) {
            return null
          }

          const materialEmissions = (walkerData.materials?.segment_nodes ?? [])
            .filter((segment) => {
              const node = supplierNodes.find((n) => n.id === segment.node_id)
              return node && node.node_type === 'material'
            })
            .map((segment) => {
              const { totalEmissions, emissionsFactor } = getGWPImpact(segment)
              return {
                materialName: segment.name,
                totalEmissions,
                amount: segment.amount,
                unit: segment.unit,
                emissionsFactor,
              }
            })

          const manufacturingEmissions = (walkerData.production?.segment_nodes ?? [])
            .filter((segment) => {
              const node = supplierNodes.find((n) => n.id === segment.node_id)
              return node && node.node_type === 'production'
            })
            .map((segment) => {
              const { totalEmissions, emissionsFactor } = getGWPImpact(segment)
              return {
                processName: segment.name,
                totalEmissions,
                amount: segment.amount,
                unit: segment.unit,
                emissionsFactor,
              }
            })

          const transportationEmissions = (
            walkerData.transportation?.segment_nodes ?? []
          )
            .filter((segment) => {
              const node = supplierNodes.find((n) => n.id === segment.node_id)
              return node && node.node_type === 'transportation'
            })
            .map((segment) => {
              const { totalEmissions, emissionsFactor } = getGWPImpact(segment)
              return {
                processName: segment.name,
                totalEmissions,
                amount: segment.amount,
                unit: segment.unit,
                emissionsFactor,
              }
            })

          const totalEmissions = [
            ...materialEmissions,
            ...manufacturingEmissions,
            ...transportationEmissions,
          ].reduce((total, emission) => total + emission.totalEmissions, 0)

          if (totalEmissions === 0) {
            const materialsTotal =
              walkerData.materials?.segment_nodes?.reduce((sum, segment) => {
                const { totalEmissions } = getGWPImpact(segment)
                return sum + totalEmissions
              }, 0) ?? 0

            const productionTotal =
              walkerData.production?.segment_nodes?.reduce((sum, segment) => {
                const { totalEmissions } = getGWPImpact(segment)
                return sum + totalEmissions
              }, 0) ?? 0

            const transportationTotal =
              walkerData.transportation?.segment_nodes?.reduce((sum, segment) => {
                const { totalEmissions } = getGWPImpact(segment)
                return sum + totalEmissions
              }, 0) ?? 0

            return createProductObject(
              product,
              [],
              [],
              [],
              materialsTotal + productionTotal + transportationTotal
            )
          }

          return createProductObject(
            product,
            materialEmissions,
            manufacturingEmissions,
            transportationEmissions,
            totalEmissions
          )
        })
      )

      return productsWithEmissions.filter(Boolean)
    }

    const supplierProducts = await getSupplierProducts()

    return {
      supplierName: supplierInfo.supplier_name,
      supplierId: supplierInfo.id,
      supplierLevel: supplierInfo.supplier_level,
      supplierType: supplierInfo.supplier_type,
      taxRegistryNumber: supplierInfo.tax_registry_number,
      primaryContactName: supplierInfo.primary_contact_name,
      primaryContactEmail: supplierInfo.primary_contact_email,
      website: supplierInfo.website,
      country: supplierInfo.primary_address.country,
      city: supplierInfo.primary_address.city,
      latitude: supplierInfo.primary_address.latitude,
      longitude: supplierInfo.primary_address.longitude,
      primaryAddress1: supplierInfo.primary_address.address_1,
      primaryAddress2: supplierInfo.primary_address.address_2,
      stateOrProvince: supplierInfo.primary_address.state_or_province,
      postalCode: supplierInfo.primary_address.postal_code,
      totalProducts: supplierProducts.length,
      products: supplierProducts,
      scope3emissions: supplierProducts.reduce(
        (totalEmissions, product) => totalEmissions + product.scope3emissions,
        0
      ),
    }
  } catch (error) {
    let errorMessage = 'Error fetching supplier info. Please try again'

    if (error.response && error.response.status === 503) {
      errorMessage =
        'Our application is starting up. Please try again in a few minutes.'
    }

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
