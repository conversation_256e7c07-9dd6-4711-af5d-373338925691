import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createProduct = async ({ product }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}`,
      sanitizeInput(product)
    )

    return {
      productId: response.data.product_id,
      productName: response.data.product_name,
      brand: response.data.brand,
      countryOfUse: response.data.country_of_use,
      functionalUnit: response.data.functional_unit,
      primaryCategory: response.data.primary_category,
      annualSalesVolumeUnits: response.data.annual_sales_volume_units,
    }
  } catch (error) {
    let errorMessage = 'Error creating product. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
