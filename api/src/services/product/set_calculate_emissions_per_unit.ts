import { context } from '@redwoodjs/graphql-server'
import { UpdateUserMetadata } from 'src/lib/auth'
import { GraphQLError } from 'graphql'

export const setCalculateEmissionsPerUnit = async ({ calculatePerUnit }) => {
  try {
    const response = await UpdateUserMetadata(
      context.currentUser.userId,
      'calculateEmissionsPerUnit',
      calculatePerUnit
    )

    return response
  } catch (error) {
    console.error(error)
    let errorMessage = 'Error updating calculateEmissionsPerUnit'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
