import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const cloneProduct = async ({ productId }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${sanitizeInput(productId)}/clone-product`
    )

    return {
      productId: response.data.product_id,
      productName: response.data.product_name,
      clonedFromProductId: response.data.cloned_from_product_id,
    }

  } catch (error) {
    let errorMessage = 'Error cloning product. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
