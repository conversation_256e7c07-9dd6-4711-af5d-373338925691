import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const updateNodes = async ({ productId, updateNodePayload }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.put(
      `${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${productId}/nodes`,
      sanitizeInput(updateNodePayload)
    )

    return true

  } catch (error) {
    let errorMessage = 'Error updating nodes. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
