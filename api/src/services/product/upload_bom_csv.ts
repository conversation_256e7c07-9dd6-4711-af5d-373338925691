import axios from 'axios'
import { GraphQLError } from 'graphql'
import Sentry from 'src/lib/sentry'
import { context } from '@redwoodjs/graphql-server'
import { base64ToBlob } from 'src/lib/helper'

// Define expected BOM fields for LLM mapping
const SYSTEM_BOM_FIELDS = [
  'raw_material', 'name', 'component_name', 'description', 'amount', 'weight',
  'unit', 'quantity', 'supplier_name', 'supplier_id', 'supplier_origin',
  'location.city', 'location.country', 'cas_number', 'recycled_content_rate',
  'scrap_rate', 'node_type', 'material'
]

interface CsvColumnMapping {
  userColumn: string
  systemField: string | null
}

// Simple CSV parser for headers and sample data
function parseCSVForHeaders(csvString: string): { headers: string[], sampleData: string[][] } {
  const lines = csvString.trim().split('\n').filter(line => line.trim().length > 0)
  if (lines.length === 0) {
    return { headers: [], sampleData: [] }
  }

  // Parse headers from first line
  const headers = lines[0].split(',').map(header => header.trim().replace(/^"|"$/g, '')).filter(header => header.length > 0)
  
  // Parse sample data from next few lines
  const sampleData: string[][] = []
  for (let i = 1; i < Math.min(4, lines.length); i++) {
    const values = lines[i].split(',').map(value => value.trim().replace(/^"|"$/g, ''))
    sampleData.push(values)
  }

  return { headers, sampleData }
}

export const uploadBomCsv = async ({ base64Data, contentType }) => {
  try {
    // Remove data URL prefix if present (e.g., "data:text/csv;base64,")
    const cleanBase64 = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data
    const csvString = Buffer.from(cleanBase64, 'base64').toString('utf8')

    // 1. Parse CSV for headers and sample data (for LLM)
    const { headers, sampleData } = parseCSVForHeaders(csvString)

    if (headers.length === 0) {
      throw new GraphQLError('CSV file appears to be empty or has no headers')
    }

    // 2. Call ML Models Service for LLM column mapping
    const llmResponse = await axios.post(
      `${process.env.ML_MODELS_ENDPOINT}/api/llm/map-columns`,
      {
        user_headers: headers,
        system_fields: SYSTEM_BOM_FIELDS,
        sample_data: sampleData,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )

    const fieldMappings = llmResponse.data
    const unmappedColumns = headers.filter(header => 
      !fieldMappings.mappings.some(mapping => 
        mapping.user_column === header && mapping.system_field
      )
    )

    // 3. Convert base64 to blob and call ML Models BOM processing endpoint
    const csvBlob = base64ToBlob(cleanBase64, contentType)
    const formData = new FormData()
    formData.append('file', csvBlob, 'bom.csv')
    formData.append('field_mappings', JSON.stringify(fieldMappings))

    const bomResponse = await axios.post(
      `${process.env.ML_MODELS_ENDPOINT}/api/bom/process-simple-bom`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    )

    const { bom_items, warnings } = bomResponse.data

    // 4. Transform BOM data to format expected by frontend
    // Following the extractFile pattern - just return data, don't create process model
    const materials = bom_items.raw_materials || []

    const materialCount = materials.length
    
    return {
      success: true,
      message: `Successfully extracted ${materialCount} materials from CSV`,
      materials: materials,
      warnings: warnings || [],
      processedRows: materialCount,
      totalRows: sampleData.length,
      unmappedColumns: unmappedColumns,
    }
  } catch (error) {
    Sentry.captureException(error)
    let errorMessage = 'Error importing BOM from CSV. Please try again.'
    
    if (error.response) {
      // Handle specific error responses from ML Models or LCA services
      if (error.response.status === 400) {
        errorMessage = error.response.data.detail || 'Invalid CSV file or data format.'
      } else if (error.response.status === 500) {
        errorMessage = 'Service temporarily unavailable. Please try again later.'
      } else {
        errorMessage = error.response.data.detail || error.response.data.message || errorMessage
      }
    } else if (error.message) {
      errorMessage = error.message
    }
    
    throw new GraphQLError(errorMessage)
  }
}