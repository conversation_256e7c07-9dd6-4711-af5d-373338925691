import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const backupProduct = async ({ productId }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.put(
      `${
        process.env.LCA_API_ENDPOINT
      }/products/${tenantID}/backup-product/${sanitizeInput(productId)}`
    )

    return true
  } catch (error) {
    let errorMessage = 'Error backing up old product. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
