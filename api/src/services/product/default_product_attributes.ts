import { context } from '@redwoodjs/graphql-server'
import axios from 'axios'
import { GraphQLError } from 'graphql'

export const getDefaultProductAttributes = async () => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo


    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/product-attributes/${tenantID}`
    )

    if (!Array.isArray(response.data)) {
      throw new Error('Unexpected response format from the server.')
    }

    return response.data.map((attribute) => ({
      id: attribute.id,
      key: attribute.key,
    }))

  } catch (error) {
    let errorMessage = 'Error fetching default product attributes. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
