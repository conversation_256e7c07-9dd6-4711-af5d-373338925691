import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const getProductProcessModel = async ({ productId }) => {
  try {
    const { urlSafeOrgName: tenantID } = context.currentUser.orgMemberInfo

    const impactFactor = context.currentUser.metadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR

    const [processModelResponse, processModelWalkerResponse] =
      await Promise.all([
        axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(productId)}/process-model`
        ),
        axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(
            productId
          )}/process-model/walk`
        ),
      ])

    const processModelData = processModelResponse.data
    const processModelWalkerData = processModelWalkerResponse.data

    const mapNode = (node) => ({
      id: node.id,
      productId: node.product_id,
      name: node.name,
      nodeType: node.node_type,
      amount: node.amount,
      unit: node.unit,
      quantity: node.quantity,
      location: {
        id: node.location.id,
        address1: node.location.address_1,
        address2: node.location.address_2,
        latitude: node.location.latitude,
        longitude: node.location.longitude,
        city: node.location.city,
        stateOrProvince: node.location.state_or_province,
        postalCode: node.location.postal_code,
        country: node.location.country,
      },
      emissionsFactor: node.emissions_factor
        ? {
            id: node.emissions_factor.id,
            activityName: node.emissions_factor.activity_name,
            geography: node.emissions_factor.geography,
            source: node.emissions_factor.source,
            activityType: node.emissions_factor.activity_type,
            referenceProduct: node.emissions_factor.reference_product,
            referenceProductAmount:
              node.emissions_factor.reference_product_amount,
            kgCO2e: node.emissions_factor.kg_co2e,
            unit: node.emissions_factor.unit,
            activityDescription: node.emissions_factor.activity_description,
          }
        : null,
    })

    const mapEdge = (edge) => ({
      productId: edge.product_id,
      fromNodeId: edge.from_node_id,
      toNodeId: edge.to_node_id,
    })

    const mapEmission = (emission) => ({
      name: emission.name,
      amount: emission.amount,
      unit: emission.unit,
      emissionsFactor: emission.emissions_factor
        ? {
            activityName: emission.emissions_factor.activity_name,
            referenceProduct: emission.emissions_factor.reference_product,
            geography: emission.emissions_factor.geography,
            source: emission.emissions_factor.source,
            kgCO2e: emission.emissions_factor.kg_co2e,
          }
        : null,
      totalEmissions: emission.total_emissions,
    })

    const mapSegment = (segment) => {
      const gwpImpact = segment.impacts?.find(
        (impact) =>
          impact.emissions_factor_value?.impact_indicator?.lcia_method ===
            impactFactor.lciaMethod &&
          impact.emissions_factor_value?.impact_indicator?.category ===
            impactFactor.categoryName &&
          impact.emissions_factor_value?.impact_indicator?.indicator ===
            impactFactor.indicator
      )

      const node = processModelData.nodes.find(node => node.id === segment.node_id)
      return {
        id: segment.node_id,
        name: node.name,
        amount: segment.amount,
        unit: segment.unit,
        emissionsFactor: gwpImpact?.emissions_factor_value?.emissions_factor
          ? {
              activityName:
                gwpImpact.emissions_factor_value.emissions_factor.activity_name,
              referenceProduct:
                gwpImpact.emissions_factor_value.emissions_factor
                  .reference_product,
              geography:
                gwpImpact.emissions_factor_value.emissions_factor.geography,
              source: gwpImpact.emissions_factor_value.emissions_factor.source,
              kgCO2e: gwpImpact.emissions_factor_value.amount,
            }
          : null,
        totalEmissions: gwpImpact?.impact_amount ?? 0,
      }
    }

    const mapWalkerCategory = (categoryData) => {
      if (!categoryData) return null

      const segments = categoryData.segment_nodes.map(mapSegment)
      const totalEmissions = segments.reduce(
        (sum, segment) => sum + (segment.totalEmissions ?? 0),
        0
      )
      return {
        totalEmissions,
        unit: segments[0]?.unit,
        emissions: segments,
      }
    }

    const walkerCategories = [
      'materials',
      'packaging',
      'production',
      'transportation',
      'use',
      'eol',
    ]

    const emissions = {}

    walkerCategories.forEach((nodeType) => {
      emissions[nodeType] = mapWalkerCategory(processModelWalkerData[nodeType])
    })

    return {
      nodes: processModelData.nodes.map(mapNode),
      edges: processModelData.edges.map(mapEdge),
      emissions,
    }
  } catch (error) {
    let errorMessage = 'Error fetching process model. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
