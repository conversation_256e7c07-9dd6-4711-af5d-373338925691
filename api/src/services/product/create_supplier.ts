import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createSupplier = async ({
  supplier,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/${tenantID}/suppliers`,
      sanitizeInput(supplier)
    )

    return {
      id: response.data.id,
      supplierName: response.data.supplier_name,
      city: response.data.primary_address?.city,
      country: response.data.primary_address?.country,
      latitude: response.data.primary_address?.latitude,
      longitude: response.data.primary_address?.longitude,
    }

  } catch (error) {
    let errorMessage = 'Error creating supplier. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
