import { context } from '@redwoodjs/graphql-server'
import axios from 'axios'
import { GraphQLError } from 'graphql'
import { sanitizeInput } from 'src/lib/helper'

export const getEFIntermediateExchanges = async (
  {
    activityName,
    referenceProduct,
    geography,
    source,
    sharedScope = false,
  }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    let url = `emissions-factors/${tenantID}/intermediate-exchanges`;
    if (sharedScope) {
      url = `emissions-factors/intermediate-exchanges`;
    }

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/${url}`,
      {
        params: {
          activity_name: sanitizeInput(activityName),
          reference_product: sanitizeInput(referenceProduct),
          geography: sanitizeInput(geography),
          source: sanitizeInput(source),
        },
      }
    )

    if (!Array.isArray(response.data)) {
      throw new Error('Unexpected response format from the server.')
    }

    return response.data.map((exchange) => ({
      id: exchange.id,
      exchangeName: exchange.exchange_name,
      amount: exchange.amount,
      unit: exchange.unit,
      exchangeEmissionsFactor: {
        id: exchange.exchange_emissions_factor.id,
        activityName: exchange.exchange_emissions_factor.activity_name,
        referenceProduct: exchange.exchange_emissions_factor.reference_product,
        geography: exchange.exchange_emissions_factor.geography,
        source: exchange.exchange_emissions_factor.source,
        amount: exchange.exchange_emissions_factor.amount,
        unit: exchange.exchange_emissions_factor.unit,
      },
      parentEmissionsFactor: {
        id: exchange.parent_emissions_factor.id,
        activityName: exchange.parent_emissions_factor.activity_name,
        referenceProduct: exchange.parent_emissions_factor.reference_product,
        geography: exchange.parent_emissions_factor.geography,
        source: exchange.parent_emissions_factor.source,
        amount: exchange.parent_emissions_factor.amount,
        unit: exchange.parent_emissions_factor.unit,
      },
    })
    )


  } catch (error) {
    let errorMessage = 'Error fetching datasets. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
