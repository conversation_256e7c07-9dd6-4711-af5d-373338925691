import axios from 'axios'
import { GraphQLError } from 'graphql'

export const searchEmissionsFactors = async ({ activityName }) => {
  try {

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/emissions-factors/`,
      {
        params: {
          activity_name: activityName,
        }
      }
    )

    if (!Array.isArray(response.data)) {
      throw new Error('Unexpected response format from the server.')
    }

    return response.data.map((emissionsFactor) => ({
      activityName: emissionsFactor.activity_name,
      referenceProduct: emissionsFactor.reference_product,
      geography: emissionsFactor.geography,
      source: emissionsFactor.source,
      unit: emissionsFactor.unit,
    }))


  } catch (error) {
    let errorMessage = 'Error fetching emissions factors. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
