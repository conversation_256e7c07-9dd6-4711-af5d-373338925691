import { context } from '@redwoodjs/graphql-server'
import axios from 'axios'
import { GraphQLError } from 'graphql'
import { sanitizeInput, buildProductEmissions } from 'src/lib/helper'
import Sentry from 'src/lib/sentry'

export const getProducts = async ({ isComponent = false }) => {
  try {
    const { urlSafeOrgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}${
        isComponent ? '?product_type=component' : '?product_type=product'
      }`
    )
    const products = response.data.products

    if (!Array.isArray(products)) {
      throw new Error('Unexpected response format from the server.')
    }

    const product_attributes = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/product-attributes/${tenantID}/all`
    )

    const _products = []

    for (const product of products) {
      const attributes = (product_attributes?.data ?? [])
        .filter((attr) => attr.product_id === product.product_id)
        .map((attr) => {
          return {
            key: attr.key,
            value: attr.value,
            productId: product.product_id,
          }
        })

      let productEmissions = null

      try {
        const walkerData = await axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(
            product.product_id
          )}/process-model/walk`
        )
        productEmissions = walkerData?.data['default']
      } catch (error) {
        console.error(`Failed to walk emissions for product ${product.product_id}`)
        console.error(error)
        Sentry.captureException(error)
      }

      const productInfoWithEmissions = buildProductEmissions(
        {
          ...product,
          attributes,
        },
        productEmissions,
        tenantID,
        context.currentUser.metadata,
      )

      _products.push(productInfoWithEmissions)
    }

    return _products
  } catch (error) {
    let errorMessage = 'Error fetching products. Please try again'

    if (error.response && error.response.status === 503) {
      errorMessage =
        'Our application is starting up. Please try again in a few minutes.'
    }

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
