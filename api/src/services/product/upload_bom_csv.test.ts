import { uploadBomCsv } from './upload_bom_csv'
import { context } from '@redwoodjs/graphql-server'

// Mock dependencies
jest.mock('axios')
jest.mock('@redwoodjs/graphql-server', () => ({
  context: {
    currentUser: {
      orgMemberInfo: {
        urlSafeOrgName: 'test-org'
      }
    }
  }
}))

jest.mock('src/lib/helper', () => ({
  base64ToBlob: jest.fn((base64Data, contentType) => {
    return new Blob(['mock csv content'], { type: contentType })
  })
}))

const mockAxios = require('axios')

describe('uploadBomCsv', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    process.env.ML_MODELS_ENDPOINT = 'http://mock-ml-service'
    process.env.LCA_API_ENDPOINT = 'http://mock-lca-service'
  })

  it('should successfully process CSV BOM upload', async () => {
    // Mock CSV data with headers
    const csvContent = 'Ingredient,Function,Total Quantity (kg)\nWater,Base/Solvent,25\nAcid,Active,2.5'
    const base64Data = Buffer.from(csvContent).toString('base64')

    // Mock ML Models service responses
    mockAxios.post
      .mockResolvedValueOnce({
        // LLM mapping response
        data: {
          mappings: [
            { user_column: 'Ingredient', system_field: 'raw_material' },
            { user_column: 'Function', system_field: 'description' },
            { user_column: 'Total Quantity (kg)', system_field: 'amount' }
          ]
        }
      })
      .mockResolvedValueOnce({
        // BOM processing response
        data: {
          bom_items: {
            raw_materials: [
              {
                raw_material: 'Water',
                weight: { amount: 25.0, unit: 'kg' },
                description: 'Base/Solvent'
              },
              {
                raw_material: 'Acid',
                weight: { amount: 2.5, unit: 'kg' },
                description: 'Active'
              }
            ]
          },
          warnings: []
        }
      })
      // No LCA service call anymore - we just return extracted data

    const result = await uploadBomCsv({
      base64Data: base64Data,
      contentType: 'text/csv'
    })

    expect(result.success).toBe(true)
    expect(result.processedRows).toBe(2)
    expect(result.materials).toHaveLength(2)
    expect(result.materials[0].raw_material).toBe('Water')
    expect(result.materials[1].raw_material).toBe('Acid')
    expect(mockAxios.post).toHaveBeenCalledTimes(2) // Only LLM mapping and BOM processing
  })

  it('should handle empty CSV gracefully', async () => {
    const csvContent = ''
    const base64Data = Buffer.from(csvContent).toString('base64')

    await expect(uploadBomCsv({
      base64Data: base64Data,
      contentType: 'text/csv'
    })).rejects.toThrow('CSV file appears to be empty or has no headers')
  })

  it('should handle CSV with only headers', async () => {
    const csvContent = 'Ingredient,Function,Total Quantity (kg)'
    const base64Data = Buffer.from(csvContent).toString('base64')

    // Mock ML Models service responses
    mockAxios.post
      .mockResolvedValueOnce({
        data: {
          mappings: [
            { user_column: 'Ingredient', system_field: 'raw_material' },
            { user_column: 'Function', system_field: 'description' },
            { user_column: 'Total Quantity (kg)', system_field: 'amount' }
          ]
        }
      })
      .mockResolvedValueOnce({
        data: {
          bom_items: {
            raw_materials: []
          },
          warnings: ['No data rows found in CSV']
        }
      })
      // No LCA service call - just return extracted data

    const result = await uploadBomCsv({
      base64Data: base64Data,
      contentType: 'text/csv'
    })

    expect(result.success).toBe(true)
    expect(result.processedRows).toBe(0)
    expect(result.materials).toHaveLength(0)
    expect(result.warnings).toContain('No data rows found in CSV')
    expect(mockAxios.post).toHaveBeenCalledTimes(2) // Only LLM and BOM processing
  })

  it('should handle data URL format base64 input', async () => {
    const csvContent = 'Ingredient,Function\nWater,Base'
    const base64Data = `data:text/csv;base64,${Buffer.from(csvContent).toString('base64')}`

    // Mock ML Models service responses
    mockAxios.post
      .mockResolvedValueOnce({
        data: {
          mappings: [
            { user_column: 'Ingredient', system_field: 'raw_material' },
            { user_column: 'Function', system_field: 'description' }
          ]
        }
      })
      .mockResolvedValueOnce({
        data: {
          bom_items: {
            raw_materials: [
              {
                raw_material: 'Water',
                weight: { amount: 25.0, unit: 'kg' },
                description: 'Base'
              }
            ]
          },
          warnings: []
        }
      })
      // No LCA service call - just return extracted data

    const result = await uploadBomCsv({
      base64Data: base64Data,
      contentType: 'text/csv'
    })

    expect(result.success).toBe(true)
    expect(result.materials).toHaveLength(1)
    expect(result.materials[0].raw_material).toBe('Water')
    expect(mockAxios.post).toHaveBeenCalledTimes(2) // Only LLM and BOM processing
  })

  it('should handle ML Models service errors', async () => {
    const csvContent = 'Ingredient,Function\nWater,Base'
    const base64Data = Buffer.from(csvContent).toString('base64')

    mockAxios.post.mockRejectedValueOnce({
      response: {
        status: 500,
        data: {
          detail: 'ML Models service error'
        }
      }
    })

    await expect(uploadBomCsv({
      base64Data: base64Data,
      contentType: 'text/csv'
    })).rejects.toThrow('Service temporarily unavailable')
  })
})