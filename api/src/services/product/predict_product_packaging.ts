import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const predictProductPackaging = async ({ productCategory, weight }) => {
  try {
    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/product-packaging/packaging-predictions`,
      {
        product_category: sanitizeInput(productCategory),
        content_weight: sanitizeInput(weight),
      }
    )

    const productPackaging = response.data.map((packaging) => {
      return {
        component: packaging.component,
        material: packaging.material,
        weight: packaging.weight.amount,
        numberOfPackages: packaging.number_of_packages,
        recycledContent: ((packaging.recycled_content_rate ?? 0) * 100),
        packagingLevel: packaging.packaging_level,
      }
    })

    return productPackaging
  } catch (error) {
    let errorMessage = 'Error predicting product category. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
