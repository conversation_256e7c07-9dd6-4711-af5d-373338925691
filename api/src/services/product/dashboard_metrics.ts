import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { logger } from 'src/lib/logger'
import { GraphQLError } from 'graphql'
import { calculateSegmentEmissions, sanitizeInput, DEFAULT_IMPACT_FACTOR, buildProductEmissions } from 'src/lib/helper'
import Sentry from 'src/lib/sentry'

export const getDashboardMetrics = async () => {
  const calculateEmissions = (products, key) => {
    return products.reduce((acc, product) => {
      acc += product[key] * (product.annualSalesVolumeUnits || 1)
      return acc
    }, 0)
  }

  const sortProductsByEmissions = (products, isAscending = true) => {
    return [...products].sort((a, b) => {
      const diff = a.totalEmissions - b.totalEmissions
      return isAscending ? diff : -diff
    })
  }

  const mapProductInfo = (product) => ({
    productName: product.productName,
    productId: product.productId,
    brand: product.brand,
    category: product.category,
    imageUrl: product.imageUrl,
    totalEmissions: product.totalEmissions,
  })

  try {
    const { urlSafeOrgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}`
    )

    const products = response.data.products?.filter(
      (product) => product.product_type === 'product'
    )

    if (!Array.isArray(products)) {
      throw new Error('Unexpected response format from the server.')
    }

    if (!products.length) {
      return {
        totalProductEmissions: 0,
        averageProductFootprint: 0,
        totalProducts: 0,
        productCategories: [],
        lifeCycleEmissionsByProductcategory: {},
        lifeCycleEmissions: {
          rawMaterialsEmissions: 0,
          packagingMaterialEmissions: 0,
          manufacturingEmissions: 0,
          distributionEmissions: 0,
          consumerUseEmissions: 0,
          eolEmissions: 0,
        },
        highestCarbonFootprintProducts: [],
        lowestCarbonFootprintProducts: [],
        productsByRegion: {},
      }
    }

    const processedProducts = []
    for (const product of products) {

      let productEmissions = null
      try {
        const walkerData = await axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(
            product.product_id
          )}/process-model/walk`
        )
        productEmissions = walkerData?.data['default']
      } catch (error) {
        console.error(`Failed to walk emissions for product ${product.product_id}`)
        console.error(error)
        Sentry.captureException(error)
      }

      const productInfoWithEmissions = buildProductEmissions(
        product,
        productEmissions,
        tenantID,
        context.currentUser.metadata,
      )

      processedProducts.push(productInfoWithEmissions)
    }

    const primaryProducts = processedProducts.filter(
      (product) => product.clonedFromProductId === null
    )

    const totalProducts = primaryProducts.length
    let totalEmissions = 0,
      totalScope3Emissions = 0

    primaryProducts.forEach((product) => {
      totalEmissions += product.totalEmissions
      totalScope3Emissions +=
        product.totalEmissions * (product.annualSalesVolumeUnits)
    })

    const averageProductFootprint = totalEmissions / totalProducts

    const sortedProducts = sortProductsByEmissions(primaryProducts)
    const highestCarbonFootprintProducts = sortedProducts
      .slice(-5)
      .map(mapProductInfo)
      .reverse()
    const lowestCarbonFootprintProducts = sortedProducts
      .slice(0, 5)
      .map(mapProductInfo)

    const dashboardMetrics = {
      totalProductEmissions: totalScope3Emissions,
      averageProductFootprint,
      totalProducts,
      productCategories: [
        ...new Set(primaryProducts.map((product) => product.category)),
      ],
      lifeCycleEmissions: {
        rawMaterialsEmissions: calculateEmissions(
          primaryProducts,
          'materialEmissions',
        ),
        packagingMaterialEmissions: calculateEmissions(
          primaryProducts,
          'packagingMaterialEmissions',
        ),
        manufacturingEmissions: calculateEmissions(
          primaryProducts,
          'manufacturingEmissions',
        ),
        distributionEmissions: calculateEmissions(
          primaryProducts,
          'distributionEmissions',
        ),
        consumerUseEmissions: calculateEmissions(
          primaryProducts,
          'consumerUseEmissions',
        ),
        eolEmissions: calculateEmissions(primaryProducts, 'eolEmissions'),
      },
      highestCarbonFootprintProducts,
      lowestCarbonFootprintProducts,
      productsByRegion: primaryProducts.reduce((acc, product) => {
        const country = product.countryOfUse.replace(/\s+/g, '').toLowerCase()
        acc[country] = (acc[country] || 0) + 1
        return acc
      }, {}),
      lifeCycleEmissionsByProductcategory: primaryProducts.reduce(
        (acc, product) => {
          const category = product.category
          if (!acc[category]) {
            acc[category] = {
              rawMaterialsEmissions: 0,
              packagingMaterialEmissions: 0,
              manufacturingEmissions: 0,
              distributionEmissions: 0,
              consumerUseEmissions: 0,
              eolEmissions: 0,
            }
          }

          acc[category].rawMaterialsEmissions +=
            product.materialEmissions *  product.annualSalesVolumeUnits
          acc[category].packagingMaterialEmissions +=
            product.packagingMaterialEmissions * product.annualSalesVolumeUnits
          acc[category].manufacturingEmissions +=
            product.manufacturingEmissions * product.annualSalesVolumeUnits
          acc[category].distributionEmissions +=
            product.distributionEmissions * product.annualSalesVolumeUnits
          acc[category].consumerUseEmissions +=
            product.consumerUseEmissions * product.annualSalesVolumeUnits
          acc[category].eolEmissions +=
            product.eolEmissions * product.annualSalesVolumeUnits

          return acc
        },
        {}
      ),
    }

    return dashboardMetrics
  } catch (error) {
    console.log(error)
    let errorMessage = 'Error fetching dashboard metrics. Please try again'

    if (error.response && error.response.status === 503) {
      errorMessage =
        'Our application is starting up. Please try again in a few minutes.'
    }

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }
    throw new GraphQLError(errorMessage)
  }
}
