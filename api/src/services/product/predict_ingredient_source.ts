import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import axiosRetry from 'axios-retry'
import { GraphQLError } from 'graphql'

axiosRetry(axios, {
  retries: 3,
  retryDelay: axiosRetry.exponentialDelay,
});

export const predictIngredientSource = async ({ ingredientName, country }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/ingredients/${tenantID}/source-of-procurement`,
      {
        params: {
          ingredient_name: sanitizeInput(ingredientName),
          country: sanitizeInput(country),
        },
      }
    )

    return {
      ingredientName: response.data.ingredient_name,
      hscode: response.data.hscode,
      productCategory: response.data.product_category,
      manufacturingCountry: response.data.manufacturing_country,
      countryCode: response.data.country_code,
      country: response.data.country,
      countryCapital: response.data.country_capital,
      latitude: response.data.latitude,
      longitude: response.data.longitude,
      locallyProcured: response.data.locally_procured,
    }

  } catch (error) {
    let errorMessage = 'Error predicting ingredient source. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
