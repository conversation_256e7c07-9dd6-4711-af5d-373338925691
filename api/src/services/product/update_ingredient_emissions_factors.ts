import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const updateIngredientEmissionsFactors = async ({ emissionsFactors }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.put(
      `${process.env.LCA_API_ENDPOINT}/ingredients/${tenantID}/emissions-factors`,
      {  emissions_factors: sanitizeInput(emissionsFactors) }
    )

    return true

  } catch (error) {
    let errorMessage = 'Error updating ingredient emissions factors. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
