import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const updateIntermediateExchange = async ({
  exchangeId,
  intermediateExchange,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.put(
      `${
        process.env.LCA_API_ENDPOINT
      }/emissions-factors/${tenantID}/exchange/${sanitizeInput(exchangeId)}`,
      sanitizeInput(intermediateExchange)
    )

    return {
      id: response.data.id,
      exchangeName: response.data.exchange_name,
      amount: response.data.amount,
      unit: response.data.unit,
    }
  } catch (error) {
    let errorMessage = 'Error updating intermediate exchange. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
