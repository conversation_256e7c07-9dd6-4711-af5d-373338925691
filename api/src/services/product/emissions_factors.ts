import axios from 'axios'
import { GraphQLError } from 'graphql'
import { context } from '@redwoodjs/graphql-server'
import { sanitizeInput } from 'src/lib/helper'

export const getEmissionsFactors = async ({ activityName, geography, source, unit }) => {
  try {

    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}/all`,
      {
        params: {
          activity_name: sanitizeInput(activityName),
          geography: sanitizeInput(geography)?.join(','),
          source: sanitizeInput(source)?.join(','),
          unit: sanitizeInput(unit)?.join(','),
        },
      }
    )

    const combinedEmissionsFactors = [
      ...response.data.shared_emissions_factors,
      ...response.data.tenant_emissions_factors.map((ef) => ({
        ...ef,
        isTenant: true
      }))
    ]

    return combinedEmissionsFactors.map((ef) => ({
      efId: ef.id,
      activityName: ef.activity_name,
      description: ef.activity_description,
      referenceProduct: ef.reference_product,
      geography: ef.geography,
      source: ef.source,
      unit: ef.unit,
      isTenant: ef.isTenant
    }))

  } catch (error) {
    let errorMessage = 'Error fetching emissions factors. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
