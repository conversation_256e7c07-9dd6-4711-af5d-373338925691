import DOMPurify from 'isomorphic-dompurify'
import he from 'he'

export const DEFAULT_IMPACT_FACTOR = {
  lciaMethod: 'ReCiPe 2016 v1.03, midpoint (H)',
  categoryName: 'climate change',
  indicator: 'global warming potential (GWP100)',
  unit: 'kg CO2-Eq',
}

export const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    return he.decode(DOMPurify.sanitize(input))
  }

  if (Array.isArray(input)) {
    return input.map(sanitizeInput)
  }

  if (typeof input === 'object' && input !== null) {
    return Object.keys(input).reduce((acc, key) => {
      acc[key] = sanitizeInput(input[key])
      return acc
    }, {})
  }

  return input
}

export const base64ToBlob = (base64, contentType) => {
  // Remove the data URL prefix if it exists
  const base64Data = base64.split(',')[1] || base64;

  const byteCharacters = atob(base64Data);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: contentType });
}

const getGwpImpact = (segment, impactFactor) => {
  return segment.impacts?.find(impact =>
    impact.emissions_factor_value?.impact_indicator?.lcia_method === impactFactor.lciaMethod &&
    impact.emissions_factor_value?.impact_indicator?.category === impactFactor.categoryName &&
    impact.emissions_factor_value?.impact_indicator?.indicator === impactFactor.indicator
  );
}
export const calculateSegmentEmissions = (segments, impactFactor) => {
  if (!segments || !Array.isArray(segments)) return 0;

  return segments.reduce((total, segment) => {
    const gwpImpact = getGwpImpact(segment, impactFactor) ?? getGwpImpact(segment, {
      lciaMethod: DEFAULT_IMPACT_FACTOR.lciaMethod,
      categoryName: DEFAULT_IMPACT_FACTOR.categoryName,
      indicator: DEFAULT_IMPACT_FACTOR.indicator,
    });
    return total + (gwpImpact?.impact_amount || 0);
  }, 0);
};

export const buildProductEmissions = (
  product,
  productEmissions,
  tenantID,
  userMetadata
) => {

  const materialEmissions = calculateSegmentEmissions(
    productEmissions?.materials?.segment_nodes,
    userMetadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR
  ) * (userMetadata?.calculateEmissionsPerUnit ? 1 : (product.annual_sales_volume_units ?? 1))

  const packagingEmissions = calculateSegmentEmissions(
    productEmissions?.packaging?.segment_nodes,
    userMetadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR
  ) * (userMetadata?.calculateEmissionsPerUnit ? 1 : (product.annual_sales_volume_units ?? 1))

  const manufacturingEmissions = calculateSegmentEmissions(
    productEmissions?.production?.segment_nodes,
    userMetadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR
  ) * (userMetadata?.calculateEmissionsPerUnit ? 1 : (product.annual_sales_volume_units ?? 1))

  const useEmissions = calculateSegmentEmissions(
    productEmissions?.use?.segment_nodes,
    userMetadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR
  ) * (userMetadata?.calculateEmissionsPerUnit ? 1 : (product.annual_sales_volume_units ?? 1))

  const transportationEmissions = calculateSegmentEmissions(
    productEmissions?.transportation?.segment_nodes,
    userMetadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR
  ) * (userMetadata?.calculateEmissionsPerUnit ? 1 : (product.annual_sales_volume_units ?? 1))

  const eolEmissions = calculateSegmentEmissions(
    productEmissions?.eol?.segment_nodes,
    userMetadata?.defaultImpactFactor?.[tenantID] ?? DEFAULT_IMPACT_FACTOR
  ) * (userMetadata?.calculateEmissionsPerUnit ? 1 : (product.annual_sales_volume_units ?? 1))

  const totalEmissions =
    materialEmissions +
    packagingEmissions +
    manufacturingEmissions +
    transportationEmissions +
    useEmissions +
    eolEmissions

  return {
    productName: product.product_name,
    productId: product.product_id,
    brand: product.brand,
    category: product.category,
    imageUrl: product.image_url,
    totalEmissions: totalEmissions ?? 0,
    materialEmissions: materialEmissions ?? 0,
    packagingMaterialEmissions: packagingEmissions ?? 0,
    manufacturingEmissions: manufacturingEmissions ?? 0,
    distributionEmissions: transportationEmissions ?? 0,
    consumerUseEmissions: useEmissions ?? 0,
    eolEmissions: eolEmissions ?? 0,
    clonedFromProductId: product.cloned_from_product_id,
    annualSalesVolumeUnits: product.annual_sales_volume_units ?? 1,
    tags: product.tags ? product.tags.split(',') : [],
    attributes: product.attributes,
    createdAt: product.created_at,
    updatedAt: product.updated_at,
    countryOfUse: product.country_of_use,
  }

}


