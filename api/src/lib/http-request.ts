import { AxiosResponse } from 'axios'

/*
Helper functions for request retries using axios.
Note: 'axios-retry' was in-effective and we encountered
issues with logging and integrating more custom logic.
*/
interface RequestFunc {
  (): Promise<AxiosResponse>;
}

async function retryRequest(retries: number, retryCount: number, request: RequestFunc): Promise<AxiosResponse | null> {
  if (retryCount > retries) {
    return null;
  }

  if (retryCount) {
    console.log(`Retry number ${retryCount}`)
  }

  try {
    const response = await request()
    if (response.status != 200) {
      throw new Error(`Non 200 response from Axios: status code ${response.status}`)
    }

    if (retryCount) {
      console.log(`Successful request on retry ${retryCount}`)
    }

    return response
  } catch (error) {
    if (!retryCount) {
      console.error(`Request failed on first request: ${error}`)
    } else {
      console.error(`Request failed on retry ${retryCount}: ${error}`)
    }

    const delayMs = 1000 * 2 ** (retryCount + 1)
    await new Promise((resolve) => setTimeout(resolve, delayMs))

    return await retryRequest(retries, retryCount + 1, request)
  }
}

export async function retryOnFail(retries: number, request: RequestFunc): Promise<AxiosResponse | null> {
  return retryRequest(retries, 0, request)
}