# Yarn's manifest file. You can configure yarn here.
# See https://yarnpkg.com/configuration/yarnrc.

# For `node_modules` (see `nodeLinker` below), this is almost always the preferred option.
compressionLevel: 0

enableGlobalCache: true

# Lets yarn use hardlinks inside `node_modules` to dedupe packages.
# For a more pnpm-like experience, consider `hardlinks-global` where hardlinks point to a global store.
nmMode: hardlinks-local

# How to install Node packages.
# Heads up: right now, <PERSON><PERSON> expects this to be `node-modules`.
nodeLinker: node-modules

yarnPath: .yarn/releases/yarn-3.6.3.cjs
