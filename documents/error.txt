2025-06-03 18:20:29.244 | INFO     | utils.geocoding:get_country_info:89 - Using cached country info for United Kingdom
INFO:     127.0.0.1:56300 - "POST /products/PlasticsCo HTTP/1.1" 200 OK
2025-06-03 18:20:39.705 | ERROR    | api.app:global_http_exception_handler:195 - Error occurred for URL: http://127.0.0.1:5005/v2/products/plasticsco/1748955029054/process-model
Exception: Process model contains disconnected nodes. The following node IDs are not connected to the main graph: {2, 3, 4, 5, 6, 7}
Request: {'request_id': '5cb7b973-ca97-40cd-a538-e70cd6a3b63b', 'url': 'http://127.0.0.1:5005/v2/products/plasticsco/1748955029054/process-model', 'method': 'POST', 'headers': {'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'user-agent': 'axios/1.8.3', 'content-length': '1354', 'accept-encoding': 'gzip, compress, deflate, br', 'host': '127.0.0.1:5005', 'connection': 'keep-alive'}, 'query_params': {}, 'client_host': '127.0.0.1', 'path_params': {'tenant_id': 'plasticsco', 'product_id': '1748955029054'}}

Traceback (most recent call last):

  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/bin/uvicorn", line 8, in <module>
    sys.exit(main())
    │   │    └ <Command main>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function BaseCommand.main at 0x103c6de10>
           └ <Command main>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x102f43c10>
         │    └ <function Command.invoke at 0x103c6e8c0>
         └ <Command main>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 5005, 'workers': 1, 'timeout_keep_alive': 120, 'app': 'api.app:app', 'uds': None, 'fd': None, 're...
           │   │      │    │           └ <click.core.Context object at 0x102f43c10>
           │   │      │    └ <function main at 0x10443aef0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x103c6d630>
           └ <click.core.Context object at 0x102f43c10>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 5005, 'workers': 1, 'timeout_keep_alive': 120, 'app': 'api.app:app', 'uds': None, 'fd': None, 're...
                       └ ()
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/uvicorn/main.py", line 416, in main
    run(
    └ <function run at 0x103d036d0>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/uvicorn/main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x103d89000>
    └ <uvicorn.server.Server object at 0x1043ada50>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x103d89090>
           │       │   └ <uvicorn.server.Server object at 0x1043ada50>
           │       └ <function run at 0x10317b400>
           └ <module 'asyncio' from '/opt/homebrew/Cellar/python@3.10/3.10.17_1/Frameworks/Python.framework/Versions/3.10/lib/python3.10/a...

  File "/opt/homebrew/Cellar/python@3.10/3.10.17_1/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x104366d50>
           │    └ <method 'run_until_complete' of 'uvloop.loop.Loop' objects>
           └ <uvloop.Loop running=True closed=False debug=False>

  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/middleware/base.py", line 149, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x13848eef0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x13848e710>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x1382d64d0>
          └ <api.app.SetupStatusMiddleware object at 0x1382d6500>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/sentry_sdk/integrations/starlette.py", line 257, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x13848eef0>
          │        │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x13848e710>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x1382d64d0>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x1382b6680>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/sentry_sdk/integrations/starlette.py", line 158, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x13848f880>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x1382d64d0>
                 └ <function ExceptionMiddleware.__call__ at 0x10541be20>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x13848f880>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x138525d80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x1381d7bb0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x1382d64d0>
          └ <function wrap_app_handling_exceptions at 0x1053c6170>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 51, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x13848e680>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          └ <fastapi.routing.APIRouter object at 0x1381d7bb0>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x13848e680>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x1381d7bb0>>
          └ <fastapi.routing.APIRouter object at 0x1381d7bb0>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x13848e680>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          │     └ <function Route.handle at 0x1053c7640>
          └ APIRoute(path='/v2/products/{tenant_id}/{product_id:path}/process-model', name='create_process_model', methods=['POST'])
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x13848e680>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          │    └ <function request_response.<locals>.app at 0x13824f0a0>
          └ APIRoute(path='/v2/products/{tenant_id}/{product_id:path}/process-model', name='create_process_model', methods=['POST'])
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x13848e680>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x11f2e8790>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x13848c940>
          └ <function wrap_app_handling_exceptions at 0x1053c6170>
> File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 51, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x13848e830>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x13848d360>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 5005), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x13848c940>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x11f2e8790>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x13824f010>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/sentry_sdk/integrations/fastapi.py", line 137, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x11f2e8790>,)
                 └ <function get_request_handler.<locals>.app at 0x13824ef80>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x1053e91b0>
  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/venv/lib/python3.10/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <services.process_model_service.ProcessModelService object at 0x11f2e8940>, 'prediction_override_service': <servi...
                 │         └ <function create_process_model at 0x11f6132e0>
                 └ Dependant(path_params=[ModelField(name='tenant_id', type=str, required=True), ModelField(name='product_id', type=str, require...

  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/api/routers/products_v2.py", line 186, in create_process_model
    verify_graph_connectivity(request.nodes, request.edges)
    │                         │       │      │       └ []
    │                         │       │      └ CreateProcessModelRequest(nodes=[CreateNode(id=1, name='Water (deionized)', amount=20.0, unit='kg', quantity=1, node_type='ma...
    │                         │       └ [CreateNode(id=1, name='Water (deionized)', amount=20.0, unit='kg', quantity=1, node_type='material', location=None, emission...
    │                         └ CreateProcessModelRequest(nodes=[CreateNode(id=1, name='Water (deionized)', amount=20.0, unit='kg', quantity=1, node_type='ma...
    └ <function verify_graph_connectivity at 0x11f5d8280>

  File "/Users/<USER>/Documents/CarbonBright/codebases/product_lca/api/routers/products_v2.py", line 168, in verify_graph_connectivity
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 400: Process model contains disconnected nodes. The following node IDs are not connected to the main graph: {2, 3, 4, 5, 6, 7}
INFO:     127.0.0.1:56345 - "POST /v2/products/plasticsco/1748955029054/process-model HTTP/1.1" 400 Bad Request