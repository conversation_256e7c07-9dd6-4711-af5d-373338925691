# ML Models Service Requirements for CSV BOM Import

## Overview

The ML Models Service needs to implement **one new endpoint** to support CSV BOM import functionality in the carbonbright-web application. This endpoint will use LLM to intelligently map user-provided CSV column headers to predefined system fields.

## Required Endpoints

### POST /api/llm/map-columns

**Purpose**: Use LLM to map user CSV column headers to system-defined BOM fields based on semantic similarity and sample data.

#### Request Specification

**HTTP Method**: `POST`
**Content-Type**: `application/json`

**Request Body Schema**:
```json
{
  "user_headers": ["string"],     // Array of column headers from user's CSV
  "system_fields": ["string"],    // Array of target system fields
  "sample_data": [["string"]]     // Optional: Array of arrays representing sample CSV rows
}
```

**Example Request**:
```json
{
  "user_headers": ["Item Name", "Quantity", "Unit of Measure", "Location City", "Supplier Name", "Unit Cost"],
  "system_fields": [
    "name", "component_name", "material", "node_type", "amount", "quantity",
    "unit", "weight", "unit_cost", "total_cost", "description", "supplier_name",
    "supplier_id", "supplier_origin", "location.city", "location.country",
    "cas_number", "recycled_content_rate", "scrap_rate"
  ],
  "sample_data": [
    ["Carbon Steel", "500", "kg", "Chicago", "Steel Corp", "1.25"],
    ["Aluminum", "200", "kg", "Detroit", "Metal Inc", "3.50"]
  ]
}
```

#### Response Specification

**HTTP Status**: `200 OK`
**Content-Type**: `application/json`

**Response Body Schema**:
```json
{
  "mappings": [
    {
      "user_column": "string",      // Original user column name
      "system_field": "string"      // Suggested system field (null if no match)
    }
  ]
}
```

**Example Response**:
```json
{
  "mappings": [
    {"user_column": "Item Name", "system_field": "component_name"},
    {"user_column": "Quantity", "system_field": "amount"},
    {"user_column": "Unit of Measure", "system_field": "unit"},
    {"user_column": "Location City", "system_field": "location.city"},
    {"user_column": "Supplier Name", "system_field": "supplier_name"},
    {"user_column": "Unit Cost", "system_field": "unit_cost"}
  ]
}
```

#### Error Handling

**HTTP Status**: `400 Bad Request` for invalid input
**HTTP Status**: `500 Internal Server Error` for LLM processing errors

**Error Response Schema**:
```json
{
  "error": "string",
  "detail": "string"
}
```

## Implementation Guidelines

### LLM Prompt Strategy
- Use semantic similarity between user headers and system fields
- Consider sample data for better context understanding
- Handle variations in naming conventions (e.g., "Qty" vs "Quantity")
- Support nested field mapping (e.g., "City" → "location.city")

### Mapping Strategy
- Focus on direct, unambiguous mappings between user headers and system fields
- Use sample data to improve context understanding
- Return null for system_field when a user column cannot be mapped

### Performance Considerations
- Target response time: < 5 seconds for typical CSV headers (10-20 columns)
- Handle up to 50 column headers efficiently
- Optimize LLM prompts for consistent, fast responses

## Testing Requirements

### Test Cases
1. **Standard BOM headers**: Common manufacturing/materials terminology
2. **Variations**: Different naming conventions, abbreviations
3. **Nested fields**: Headers that map to nested JSON properties
4. **Unmappable columns**: Headers that don't match any system field
5. **Edge cases**: Empty headers, special characters, very long names

### Sample Test Data
```json
{
  "user_headers": ["Component", "Qty", "UOM", "Material Type", "Supplier", "Cost/Unit"],
  "system_fields": ["component_name", "amount", "unit", "material", "supplier_name", "unit_cost"],
  "sample_data": [["Steel Plate", "100", "kg", "Raw Material", "Steel Corp", "2.50"]]
}
```

**Expected Response**:
```json
{
  "mappings": [
    {"user_column": "Component", "system_field": "component_name"},
    {"user_column": "Qty", "system_field": "amount"},
    {"user_column": "UOM", "system_field": "unit"},
    {"user_column": "Material Type", "system_field": "material"},
    {"user_column": "Supplier", "system_field": "supplier_name"},
    {"user_column": "Cost/Unit", "system_field": "unit_cost"}
  ]
}
```

## Integration Notes

- This endpoint will be called by carbonbright-web backend service
- No authentication required if called from internal network
- Consider rate limiting for production deployment
- Log mapping results for continuous improvement

## Success Criteria

1. ✅ Endpoint correctly maps common BOM terminology
2. ✅ Provides clear mapping between user headers and system fields
3. ✅ Handles edge cases gracefully
4. ✅ Responds within performance targets
5. ✅ Integrates successfully with carbonbright-web

## Implementation Status

### Completed ✅

1. **POST /api/llm/map-columns endpoint**
   - Location: `bom_processing/column_mapper.py`
   - Uses OpenAI GPT-4 for semantic column mapping
   - Supports sample data context for better accuracy
   - Returns structured JSON with user_column → system_field mappings
   - Handles unmappable columns by returning null

2. **POST /api/bom/process-simple-bom endpoint**
   - Location: `bom_processing/processor.py`
   - Processes CSV files using field mappings from map-columns endpoint
   - Converts all weights to kilograms for consistency
   - Extracts material information including supplier details
   - Provides detailed warnings for data issues

3. **Module Structure**
   - Created new `bom_processing` module to keep functionality separate
   - Added Pydantic models for request/response validation
   - Integrated with existing LLM patterns from the codebase
   - Added comprehensive error handling and logging

4. **Tests**
   - Created test suite in `bom_processing/tests/`
   - Tests for column mapping variations and edge cases
   - Tests for BOM processing with different units and formats
   - Tests for error handling scenarios

5. **Integration**
   - Updated `main.py` to include new BOM routes
   - Routes available at specified endpoints
   - Ready for integration with carbonbright-web

### Test Results ✅

All tests passing (10/10):
- ✅ test_process_simple_bom_basic
- ✅ test_process_bom_with_units
- ✅ test_process_bom_with_supplier_info
- ✅ test_process_bom_missing_weight
- ✅ test_process_bom_invalid_file_type
- ✅ test_process_bom_invalid_field_mappings
- ✅ test_process_bom_missing_mappings_key
- ✅ test_process_bom_empty_csv
- ✅ test_process_bom_weight_variations
- ✅ test_health_check

### Frontend Integration Guide

#### 1. Column Mapping Endpoint

**Request:**
```bash
POST /api/llm/map-columns
Content-Type: application/json

{
  "user_headers": ["Ingredient", "Function", "Total Quantity (kg)"],
  "system_fields": ["raw_material", "description", "amount", "weight", "unit"],
  "sample_data": [
    ["Water (deionized)", "Base/Solvent", "25"],
    ["Hyaluronic Acid", "Hydration Agent", "2.5"]
  ]
}
```

**Response:**
```json
{
  "mappings": [
    {"user_column": "Ingredient", "system_field": "raw_material"},
    {"user_column": "Function", "system_field": "description"},
    {"user_column": "Total Quantity (kg)", "system_field": "amount"}
  ]
}
```

**Key Points:**
- LLM intelligently maps variations (e.g., "Qty" → "amount", "UOM" → "unit")
- Returns `null` for `system_field` if no match found
- Sample data improves mapping accuracy
- Response time typically < 5 seconds

#### 2. BOM Processing Endpoint

**Request:**
```bash
POST /api/bom/process-simple-bom
Content-Type: multipart/form-data

field_mappings: {"mappings":[{"user_column":"Ingredient","system_field":"raw_material"},{"user_column":"Function","system_field":"description"},{"user_column":"Total Quantity (kg)","system_field":"amount"}]}
file: [CSV file]
```

**Response:**
```json
{
  "bom_items": {
    "raw_materials": [
      {
        "raw_material": "Water (deionized)",
        "weight": {
          "amount": 25.0,
          "unit": "kg"
        },
        "description": "Base/Solvent",
        "supplier_name": null,
        "supplier_location": null
      },
      {
        "raw_material": "Hyaluronic Acid",
        "weight": {
          "amount": 2.5,
          "unit": "kg"
        },
        "description": "Hydration/Plumping Agent",
        "supplier_name": null,
        "supplier_location": null
      }
    ]
  },
  "warnings": []
}
```

**Key Points:**
- All weights converted to kg for consistency
- Missing weights default to 1.0 kg with warning
- Supplier location has nested structure (city, country)
- Warnings array provides non-critical issues

### Error Handling

1. **Empty CSV:**
   - Status: 400
   - Response: `{"detail": "CSV file is empty", ...}`

2. **Invalid File Type:**
   - Status: 400
   - Response: `{"detail": "Only CSV files are supported", ...}`

3. **Invalid JSON:**
   - Status: 400
   - Response: `{"detail": "Invalid JSON in field_mappings: ...", ...}`

4. **Missing Mappings:**
   - Status: 400
   - Response: `{"detail": "field_mappings must contain a 'mappings' key", ...}`

### Implementation Notes for Frontend

1. **File Upload Flow:**
   - User uploads CSV → Extract headers
   - Call `/api/llm/map-columns` with headers and sample rows
   - Display mapping suggestions to user (allow editing)
   - Submit file + mappings to `/api/bom/process-simple-bom`

2. **Unit Handling:**
   - Backend converts all weights to kg
   - Supports: kg, g, lb, oz, t (tons)
   - Display original units in UI if needed

3. **Data Validation:**
   - Backend handles missing/invalid data gracefully
   - Check `warnings` array for data quality issues
   - Empty materials array indicates no valid data extracted

4. **Performance:**
   - Column mapping: < 5 seconds (LLM call)
   - BOM processing: < 2 seconds for typical files

### Complete Usage Example

```javascript
// Frontend pseudocode
async function importBOM(csvFile) {
  // 1. Read CSV headers and sample data
  const headers = extractHeaders(csvFile);
  const sampleData = extractSampleRows(csvFile, 3);

  // 2. Get column mappings from LLM
  const mappingResponse = await fetch('/api/llm/map-columns', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      user_headers: headers,
      system_fields: SYSTEM_FIELDS,
      sample_data: sampleData
    })
  });

  const mappings = await mappingResponse.json();

  // 3. Show mappings to user for review/edit
  const confirmedMappings = await showMappingDialog(mappings);

  // 4. Process the BOM
  const formData = new FormData();
  formData.append('file', csvFile);
  formData.append('field_mappings', JSON.stringify(confirmedMappings));

  const bomResponse = await fetch('/api/bom/process-simple-bom', {
    method: 'POST',
    body: formData
  });

  const bomData = await bomResponse.json();

  // 5. Display results
  if (bomData.warnings.length > 0) {
    showWarnings(bomData.warnings);
  }
  displayBOMItems(bomData.bom_items.raw_materials);
}
```

## Implementation Flow

This section describes the complete flow for processing Bill of Materials CSV files, from upload to rendering:

### Step 1: CSV Upload
- Frontend allows the user to upload a CSV file containing BOM data
- CSV has user-defined column headers that may not match our system fields

### Step 2: Column Mapping
- Backend extracts column headers from the uploaded CSV
- Backend sends headers to the `/api/llm/map-columns` endpoint
- LLM analyzes headers and returns mappings between user columns and system fields
- This step needs to complete before proceeding to data processing

### Step 3: BOM Processing
- Once mappings are determined, backend processes the full CSV using the `/api/bom/process-simple-bom` endpoint
- System applies the field mappings to extract and structure the data
- Data is transformed into the standardized BOM format

### Step 4: Frontend Rendering
- Frontend receives the processed data with proper field associations
- Data is displayed to the user in a standardized format
- User can review, edit, and confirm the data before final submission

This approach separates concerns between mapping and processing, allowing for more robust error handling and user feedback at each stage.

## BOM Processing Functionality

This section outlines the BOM processing functionality required for handling simpler bills of materials, leveraging concepts from the existing `/api/file-extraction/` endpoint.

### POST /api/bom/process-simple-bom

**Purpose**: Process a simple Bill of Materials CSV file and convert it into a structured format suitable for carbon footprint calculations.

#### Request Specification

**HTTP Method**: `POST`
**Content-Type**: `multipart/form-data`

**Request Parameters**:
- `file`: The CSV file containing the BOM data (required)
- `field_mappings`: JSON string containing the mappings from CSV headers to system fields (required)

**Example Field Mappings**:
```json
{
  "mappings": [
    {"user_column": "Ingredient", "system_field": "raw_material"},
    {"user_column": "Function", "system_field": "description"},
    {"user_column": "Total Quantity (kg)", "system_field": "amount"}
  ]
}
```

#### Response Specification

**HTTP Status**: `200 OK`
**Content-Type**: `application/json`

**Response Body Schema**:
```json
{
  "bom_items": {
    "raw_materials": [
      {
        "raw_material": "string",
        "weight": {
          "amount": "number",
          "unit": "string"
        },
        "description": "string",
        "supplier_name": "string",
        "supplier_location": {
          "city": "string",
          "country": "string"
        }
      }
    ]
  },
  "warnings": ["string"]
}
```

**Example Response**:
```json
{
  "bom_items": {
    "raw_materials": [
      {
        "raw_material": "Water (deionized)",
        "weight": {
          "amount": 25.0,
          "unit": "kg"
        },
        "description": "Base/Solvent"
      },
      {
        "raw_material": "Hyaluronic Acid",
        "weight": {
          "amount": 2.5,
          "unit": "kg"
        },
        "description": "Hydration/Plumping Agent"
      }
    ]
  },
  "warnings": []
}
```

#### Error Handling

**HTTP Status**: `400 Bad Request` for invalid input or unsupported file format
**HTTP Status**: `500 Internal Server Error` for processing errors

**Error Response Schema**:
```json
{
  "error": "string",
  "detail": "string"
}
```

### Implementation Guidelines Based on Existing Code

Based on the existing `/api/file-extraction/` endpoint implementation, the following patterns can be leveraged:

1. **File Reading & Validation**:
   - Use `pd.read_csv()` to read the uploaded CSV file
   - Validate presence of required columns based on field mappings
   - Handle file format errors with appropriate error messages

2. **Data Processing**:
   - Process one row at a time to extract material information
   - Convert weights to consistent units (e.g., kg)
   - Apply field mappings to transform user columns to system fields

3. **Simplified Data Model**:
   - Focus on `raw_materials` extraction without the complex node/edge structure
   - Include essential fields: material name, weight, description, supplier info
   - Omit advanced features like recycled content processing unless explicitly needed

4. **Performance Optimization**:
   - Process files in memory without complex transformations
   - Target processing time < 2 seconds for typical BOM files (10-50 rows)

5. **Error & Warning Collection**:
   - Collect warnings for non-critical issues (e.g., missing optional data)
   - Return warnings alongside successful results
   - Return appropriate HTTP errors for critical processing failures

### Code Structure Recommendations

```python
async def process_simple_bom(csv_file, field_mappings):
    """
    Process a simple BOM CSV file using provided field mappings.

    Args:
        csv_file: The uploaded CSV file
        field_mappings: Mapping of user columns to system fields

    Returns:
        Dictionary with processed BOM items and any warnings
    """
    try:
        # Read CSV file
        with tempfile.NamedTemporaryFile() as tmp:
            tmp.write(await csv_file.read())
            tmp.flush()
            df = pd.read_csv(tmp.name)

        # Process data based on mappings
        raw_materials = []
        warnings = []

        # Extract data using mappings
        for _, row in df.iterrows():
            material = extract_material_from_row(row, field_mappings, warnings)
            if material:
                raw_materials.append(material)

        return {
            "bom_items": {
                "raw_materials": raw_materials
            },
            "warnings": warnings
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing BOM file: {str(e)}"
        )
```

### Testing Recommendations

1. **Test with Sample Files**:
   - Simple BOM like the anti-aging cream example
   - BOM with missing columns
   - BOM with different units (g, kg, lb)

2. **Edge Cases**:
   - Empty files
   - Files with duplicate materials
   - Files with invalid numeric values

3. **Integration Testing**:
   - Test the full flow: column mapping + BOM processing
   - Verify correct application of field mappings

## Code References from Existing Implementation

This section provides specific code references from the existing `/api/file-extraction/` endpoint to help with implementation:

### File Extraction API References

The main implementation can be found in these files:
- `file_extraction/api.py`: Contains the endpoint definitions and request handling
- `file_extraction/beta.py`: Contains the core BOM processing logic

### Key Functions to Reference

1. **CSV Reading and Validation**:
   ```python
   # From file_extraction/api.py
   def get_bom_items_from_structured_table(filename: str, weight_regex: str, weight_column: str, ...):
       # Example reading CSV and processing columns
       df = pd.read_csv(filename)
       pattern = re.compile(weight_regex)
       df[weight_column] = df[weight_column].apply(lambda x: re.findall(pattern, str(x)))
       # ...processing logic...
   ```

2. **FastAPI Endpoint Structure**:
   ```python
   # From file_extraction/api.py
   @model_api.post("/components")
   async def extract_components(file: UploadFile = File(...)) -> ComponentsResponse:
       try:
           with tempfile.NamedTemporaryFile() as tmp:
               tmp.write(await file.read())
               tmp.flush()
               tmp_path = tmp.name
               result = await get_components_from_file(tmp_path)
               # ...processing logic...
           return ComponentsResponse(components=components_, warnings=result.warnings)
       except Exception as e:
           raise HTTPException(status_code=500, detail=str(e))
   ```

3. **Column Validation Logic**:
   ```python
   # From file_extraction/beta.py
   def validate_columns(df: pd.DataFrame, required_column_groups: List[Set[str]], warnings: List[str]) -> None:
       """
       Validate that DataFrame contains required columns.
       For each group in required_column_groups, at least one column must be present.
       """
       # Implementation example...
   ```

4. **Row Processing Logic**:
   ```python
   # From file_extraction/beta.py
   # Example of row-by-row processing
   for _, row in df.iterrows():
       # Extract values from the row based on column mappings
       weight = row[weight_column]
       if weight:
           # Process the weight value
           # Add to output collection
   ```

5. **Unit Conversion and Standardization**:
   ```python
   # From file_extraction/beta.py
   def extract_weight(weight_str: str) -> float:
       # Handle case when weight is already a float or int
       if isinstance(weight_str, (float, int)):
           return float(weight_str)

       # weight can be in format "100g" or "100 - 200g"
       # Extract the numeric part
       weight_part = weight_str.split(" - ")[-1].rstrip("g").strip()

       # Convert to float
       return float(weight_part)
   ```

These code snippets can serve as a reference for implementing the new endpoints, adapting the patterns to fit the simpler BOM processing requirements.
