{
  "version": "0.3.0",
  "configurations": [
    {
      "command": "yarn redwood dev --apiDebugPort 18911",
      "name": "Run Dev Server",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "name": "Attach API debugger",
      "port": 18911, // you can change this port, see https://redwoodjs.com/docs/project-configuration-dev-test-build#debugger-configuration
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "localRoot": "${workspaceFolder}/node_modules/@redwoodjs/api-server/dist",
      "remoteRoot": "${workspaceFolder}/node_modules/@redwoodjs/api-server/dist",
      "sourceMaps": true,
      "restart": true
    },
    {
      "command": "yarn redwood test api",
      "name": "Test api",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "command": "yarn redwood test web",
      "name": "Test web",
      "request": "launch",
      "type": "node-terminal"
    },
  ]
}
