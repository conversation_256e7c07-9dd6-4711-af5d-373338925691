# This file contains the configuration settings for your Redwood app.
# This file is also what makes your Redwood app a Redwood app.
# If you remove it and try to run `yarn rw dev`, you'll get an error.
#
# For the full list of options, see the "App Configuration: redwood.toml" doc:
# https://redwoodjs.com/docs/app-configuration-redwood-toml

[web]
  title = "CarbonBright"
  port = 8910
  apiUrl = "/.redwood/functions" # You can customize graphql and dbauth urls individually too: see https://redwoodjs.com/docs/app-configuration-redwood-toml#api-paths
  includeEnvironmentVariables = ['PROPELAUTH_AUTH_URL', 'SERVICE_HUB_PORTAL_ID', 'SENTRY_DSN']
[api]
  port = 8911
  includeEnvironmentVariables = ['LCA_API_ENDPOINT', 'PROPELAUTH_AUTH_URL', 'PROPELAUTH_API_TOKEN', 'PROPELAUTH_VERIFIER_KEY', 'MAPBOX_SECRET_TOKEN', 'MAPBOX_ACCOUNT_USERNAME', 'ML_MODELS_ENDPOINT', 'SENTRY_DSN', 'CONTACT_SUPPORT_EMAIL', 'CONTACT_SUPPORT_PASSWORD', 'CONTACT_SUPPORT_RECIPIENTS', 'CACHE_TTL']
[browser]
  open = true
  includeEnvironmentVariables = ['PROPELAUTH_AUTH_URL', 'SERVICE_HUB_PORTAL_ID']
[notifications]
  versionUpdates = ["latest"]
