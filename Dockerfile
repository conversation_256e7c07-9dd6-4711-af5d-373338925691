FROM node:20-alpine

WORKDIR /app

ENV GENERATE_SOURCEMAP=false
ENV NODE_OPTIONS=--max-old-space-size=4096

COPY .env ./
COPY package.json yarn.lock ./

COPY api ./api
COPY web ./web

COPY redwood.toml ./
COPY graphql.config.js ./
COPY scripts ./scripts
COPY tests ./tests
COPY playwright.config.ts ./

COPY .yarnrc.yml ./
COPY .yarn ./.yarn

RUN yarn workspace api add @babel/plugin-transform-runtime
RUN yarn install

RUN mkdir .redwood

RUN yarn rw build web
RUN yarn rw build api

RUN rm -rf web/src
RUN rm -rf api/src

EXPOSE 8910

CMD ["yarn", "rw", "serve"]