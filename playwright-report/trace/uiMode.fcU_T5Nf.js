import{u as Ct,r as V,d as yt,_ as Bt,e as Mt,f as Nt,j as l,R as f,g as Rt,c as Pt,s as wt,S as Dt,h as G,T as y,t as Lt,m as Ft,i as It,k as dt,W as Wt,M as Ot,l as At,a as zt,b as Vt}from"./assets/wsPort-964mA9MZ.js";class ht{constructor(){this._stringCache=new Map}internString(t){let e=this._stringCache.get(t);return e||(this._stringCache.set(t,t),e=t),e}}var Kt={};class ft{constructor(t,e,s,i){this._tests=new Map,this._listOnly=!1,this._clearPreviousResultsWhenTestBegins=!1,this._stringPool=new ht,this._rootSuite=new W("","root"),this._pathSeparator=t,this._reporter=e,this._reuseTestCases=s,this._reportConfig=i}dispatch(t){const{method:e,params:s}=t;if(e==="onConfigure"){this._onConfigure(s.config);return}if(e==="onProject"){this._onProject(s.project);return}if(e==="onBegin"){this._onBegin();return}if(e==="onTestBegin"){this._onTestBegin(s.testId,s.result);return}if(e==="onTestEnd"){this._onTestEnd(s.test,s.result);return}if(e==="onStepBegin"){this._onStepBegin(s.testId,s.resultId,s.step);return}if(e==="onStepEnd"){this._onStepEnd(s.testId,s.resultId,s.step);return}if(e==="onError"){this._onError(s.error);return}if(e==="onStdIO"){this._onStdIO(s.type,s.testId,s.resultId,s.data,s.isBase64);return}if(e==="onEnd")return this._onEnd(s.result);if(e==="onExit")return this._onExit()}_setClearPreviousResultsWhenTestBegins(){this._clearPreviousResultsWhenTestBegins=!0}_onConfigure(t){var e,s;this._rootDir=t.rootDir,this._listOnly=t.listOnly,this._config=this._parseConfig(t),(s=(e=this._reporter).onConfigure)==null||s.call(e,this._config)}_onProject(t){let e=this._rootSuite.suites.find(s=>s.project().__projectId===t.id);if(e||(e=new W(t.name,"project"),this._rootSuite.suites.push(e),e.parent=this._rootSuite),e._project=this._parseProject(t),this._mergeSuitesInto(t.suites,e),this._listOnly){const s=new Set,i=o=>{o.tests.map(c=>c.testId).forEach(c=>s.add(c)),o.suites.forEach(i)};t.suites.forEach(i);const r=o=>{o.tests=o.tests.filter(c=>s.has(c.id)),o.suites.forEach(r)};r(e)}}_onBegin(){var t,e;(e=(t=this._reporter).onBegin)==null||e.call(t,this._rootSuite)}_onTestBegin(t,e){var r,o;const s=this._tests.get(t);this._clearPreviousResultsWhenTestBegins&&s._clearResults();const i=s._createTestResult(e.id);i.retry=e.retry,i.workerIndex=e.workerIndex,i.parallelIndex=e.parallelIndex,i.setStartTimeNumber(e.startTime),i.statusEx="running",(o=(r=this._reporter).onTestBegin)==null||o.call(r,s,i)}_onTestEnd(t,e){var r,o,c;const s=this._tests.get(t.testId);s.timeout=t.timeout,s.expectedStatus=t.expectedStatus,s.annotations=t.annotations;const i=s.resultsMap.get(e.id);i.duration=e.duration,i.status=e.status,i.statusEx=e.status,i.errors=e.errors,i.error=(r=i.errors)==null?void 0:r[0],i.attachments=this._parseAttachments(e.attachments),(c=(o=this._reporter).onTestEnd)==null||c.call(o,s,i),i.stepMap=new Map}_onStepBegin(t,e,s){var p,d;const i=this._tests.get(t),r=i.resultsMap.get(e),o=s.parentStepId?r.stepMap.get(s.parentStepId):void 0,c=this._absoluteLocation(s.location),h=new Ht(s,o,c);o?o.steps.push(h):r.steps.push(h),r.stepMap.set(s.id,h),(d=(p=this._reporter).onStepBegin)==null||d.call(p,i,r,h)}_onStepEnd(t,e,s){var c,h;const i=this._tests.get(t),r=i.resultsMap.get(e),o=r.stepMap.get(s.id);o.duration=s.duration,o.error=s.error,(h=(c=this._reporter).onStepEnd)==null||h.call(c,i,r,o)}_onError(t){var e,s;(s=(e=this._reporter).onError)==null||s.call(e,t)}_onStdIO(t,e,s,i,r){var p,d,v,g;const o=r?globalThis.Buffer?Buffer.from(i,"base64"):atob(i):i,c=e?this._tests.get(e):void 0,h=c&&s?c.resultsMap.get(s):void 0;t==="stdout"?(h==null||h.stdout.push(o),(d=(p=this._reporter).onStdOut)==null||d.call(p,o,c,h)):(h==null||h.stderr.push(o),(g=(v=this._reporter).onStdErr)==null||g.call(v,o,c,h))}async _onEnd(t){var e,s;await((s=(e=this._reporter).onEnd)==null?void 0:s.call(e,{status:t.status,startTime:new Date(t.startTime),duration:t.duration}))}_onExit(){var t,e;return this._stringPool=new ht,(e=(t=this._reporter).onExit)==null?void 0:e.call(t)}_parseConfig(t){const e={...xt,...t};return this._reportConfig&&(e.configFile=this._reportConfig.configFile,e.reportSlowTests=this._reportConfig.reportSlowTests,e.quiet=this._reportConfig.quiet,e.reporter=[...this._reportConfig.reporter]),e}_parseProject(t){return{__projectId:t.id,metadata:t.metadata,name:t.name,outputDir:this._absolutePath(t.outputDir),repeatEach:t.repeatEach,retries:t.retries,testDir:this._absolutePath(t.testDir),testIgnore:Z(t.testIgnore),testMatch:Z(t.testMatch),timeout:t.timeout,grep:Z(t.grep),grepInvert:Z(t.grepInvert),dependencies:t.dependencies,teardown:t.teardown,snapshotDir:this._absolutePath(t.snapshotDir),use:{}}}_parseAttachments(t){return t.map(e=>({...e,body:e.base64&&globalThis.Buffer?Buffer.from(e.base64,"base64"):void 0}))}_mergeSuitesInto(t,e){for(const s of t){let i=e.suites.find(r=>r.title===s.title);i||(i=new W(s.title,s.type),i.parent=e,e.suites.push(i)),i.location=this._absoluteLocation(s.location),i._fileId=s.fileId,i._parallelMode=s.parallelMode,this._mergeSuitesInto(s.suites,i),this._mergeTestsInto(s.tests,i)}}_mergeTestsInto(t,e){for(const s of t){let i=this._reuseTestCases?e.tests.find(r=>r.title===s.title):void 0;i||(i=new Ut(s.testId,s.title,this._absoluteLocation(s.location)),i.parent=e,e.tests.push(i),this._tests.set(i.id,i)),this._updateTest(s,i)}}_updateTest(t,e){return e.id=t.testId,e.location=this._absoluteLocation(t.location),e.retries=t.retries,e.tags=t.tags??[],e}_absoluteLocation(t){return t&&{...t,file:this._absolutePath(t.file)}}_absolutePath(t){return t&&this._stringPool.internString(this._rootDir+this._pathSeparator+t)}}class W{constructor(t,e){this._requireFile="",this.suites=[],this.tests=[],this._parallelMode="none",this.title=t,this._type=e}allTests(){const t=[],e=s=>{for(const i of[...s.suites,...s.tests])i instanceof W?e(i):t.push(i)};return e(this),t}titlePath(){const t=this.parent?this.parent.titlePath():[];return(this.title||this._type!=="describe")&&t.push(this.title),t}project(){var t;return this._project??((t=this.parent)==null?void 0:t.project())}}class Ut{constructor(t,e,s){this.fn=()=>{},this.results=[],this.expectedStatus="passed",this.timeout=0,this.annotations=[],this.retries=0,this.tags=[],this.repeatEachIndex=0,this.resultsMap=new Map,this.id=t,this.title=e,this.location=s}titlePath(){const t=this.parent?this.parent.titlePath():[];return t.push(this.title),t}outcome(){var s,i;const t=[...this.results];for(;((s=t[0])==null?void 0:s.status)==="skipped"||((i=t[0])==null?void 0:i.status)==="interrupted";)t.shift();if(!t.length)return"skipped";const e=t.filter(r=>r.status!=="skipped"&&r.status!=="interrupted"&&r.status!==this.expectedStatus);return e.length?e.length===t.length?"unexpected":"flaky":"expected"}ok(){const t=this.outcome();return t==="expected"||t==="flaky"||t==="skipped"}_clearResults(){this.results=[],this.resultsMap.clear()}_createTestResult(t){const e=new $t(this.results.length);return this.results.push(e),this.resultsMap.set(t,e),e}}class Ht{constructor(t,e,s){this.duration=-1,this.steps=[],this._startTime=0,this.title=t.title,this.category=t.category,this.location=s,this.parent=e,this._startTime=t.startTime}titlePath(){var e;return[...((e=this.parent)==null?void 0:e.titlePath())||[],this.title]}get startTime(){return new Date(this._startTime)}set startTime(t){this._startTime=+t}}class $t{constructor(t){this.parallelIndex=-1,this.workerIndex=-1,this.duration=-1,this.stdout=[],this.stderr=[],this.attachments=[],this.status="skipped",this.steps=[],this.errors=[],this.stepMap=new Map,this.statusEx="scheduled",this._startTime=0,this.retry=t}setStartTimeNumber(t){this._startTime=t}get startTime(){return new Date(this._startTime)}set startTime(t){this._startTime=+t}}const xt={forbidOnly:!1,fullyParallel:!1,globalSetup:null,globalTeardown:null,globalTimeout:0,grep:/.*/,grepInvert:null,maxFailures:0,metadata:{},preserveOutput:"always",projects:[],reporter:[[Kt.CI?"dot":"list"]],reportSlowTests:{max:5,threshold:15e3},configFile:"",rootDir:"",quiet:!1,shard:null,updateSnapshots:"missing",version:"",workers:0,webServer:null};function Z(n){return n.map(t=>t.s?t.s:new RegExp(t.r.source,t.r.flags))}const qt=({source:n})=>{const[t,e]=Ct(),[s,i]=V.useState(yt()),[r]=V.useState(Bt(()=>import("./assets/xtermModule-Yt6xwiJ_.js"),__vite__mapDeps([0,1]),import.meta.url).then(c=>c.default)),o=V.useRef(null);return V.useEffect(()=>(Mt(i),()=>Nt(i)),[]),V.useEffect(()=>{const c=n.write,h=n.clear;return(async()=>{const{Terminal:p,FitAddon:d}=await r,v=e.current;if(!v)return;const g=s==="dark-mode"?Xt:Yt;if(o.current&&o.current.terminal.options.theme===g)return;o.current&&(v.textContent="");const a=new p({convertEol:!0,fontSize:13,scrollback:1e4,fontFamily:"var(--vscode-editor-font-family)",theme:g}),m=new d;a.loadAddon(m);for(const x of n.pending)a.write(x);n.write=x=>{n.pending.push(x),a.write(x)},n.clear=()=>{n.pending=[],a.clear()},a.open(v),m.fit(),o.current={terminal:a,fitAddon:m}})(),()=>{n.clear=h,n.write=c}},[r,o,e,n,s]),V.useEffect(()=>{setTimeout(()=>{o.current&&(o.current.fitAddon.fit(),n.resize(o.current.terminal.cols,o.current.terminal.rows))},250)},[t,n]),l.jsx("div",{"data-testid":"output",className:"xterm-wrapper",style:{flex:"auto"},ref:e})},Yt={foreground:"#383a42",background:"#fafafa",cursor:"#383a42",black:"#000000",red:"#e45649",green:"#50a14f",yellow:"#c18401",blue:"#4078f2",magenta:"#a626a4",cyan:"#0184bc",white:"#a0a0a0",brightBlack:"#000000",brightRed:"#e06c75",brightGreen:"#98c379",brightYellow:"#d19a66",brightBlue:"#4078f2",brightMagenta:"#a626a4",brightCyan:"#0184bc",brightWhite:"#383a42",selectionBackground:"#d7d7d7",selectionForeground:"#383a42"},Xt={foreground:"#f8f8f2",background:"#1e1e1e",cursor:"#f8f8f0",black:"#000000",red:"#ff5555",green:"#50fa7b",yellow:"#f1fa8c",blue:"#bd93f9",magenta:"#ff79c6",cyan:"#8be9fd",white:"#bfbfbf",brightBlack:"#4d4d4d",brightRed:"#ff6e6e",brightGreen:"#69ff94",brightYellow:"#ffffa5",brightBlue:"#d6acff",brightMagenta:"#ff92df",brightCyan:"#a4ffff",brightWhite:"#e6e6e6",selectionBackground:"#44475a",selectionForeground:"#f8f8f2"},Jt=({title:n,children:t,setExpanded:e,expanded:s,expandOnTitleClick:i})=>l.jsxs("div",{className:"expandable"+(s?" expanded":""),children:[l.jsxs("div",{className:"expandable-title",onClick:()=>i&&e(!s),children:[l.jsx("div",{className:"codicon codicon-"+(s?"chevron-down":"chevron-right"),style:{cursor:"pointer",color:"var(--vscode-foreground)",marginLeft:"5px"},onClick:()=>!i&&e(!s)}),n]}),s&&l.jsx("div",{style:{marginLeft:25},children:t})]});function Qt(n){return`.playwright-artifacts-${n}`}let lt=()=>{},vt=n=>{},_t={cols:80,rows:24},I=async()=>{};const O={pending:[],clear:()=>{},write:n=>O.pending.push(n),resize:(n,t)=>{_t={cols:n,rows:t},q("resizeTerminal",{cols:n,rows:t})}},Zt=({})=>{var ut;const[n,t]=f.useState(""),[e,s]=f.useState(!1),[i,r]=f.useState(new Map([["passed",!1],["failed",!1],["skipped",!1]])),[o,c]=f.useState(new Map),[h,p]=f.useState({config:void 0,rootSuite:void 0,loadErrors:[]}),[d,v]=f.useState(),[g,a]=f.useState({}),[m,x]=f.useState(new Set),[T,B]=f.useState(!1),[_,R]=f.useState(),[M,X]=Rt("watch-all",!1),[et,A]=f.useState({value:new Set}),u=f.useRef(Promise.resolve()),S=f.useRef(new Set),[w,b]=f.useState(0),[k,E]=f.useState(!1),[J,ct]=f.useState(!0),Tt=f.useRef(null),st=f.useCallback(()=>{B(!0),A({value:new Set}),lt(xt,new W("","root"),[],void 0),bt(!0).then(async()=>{B(!1);const{hasBrowsers:j}=await I("checkBrowsers");ct(j)})},[]);f.useEffect(()=>{var j;(j=Tt.current)==null||j.focus(),B(!0),Pt({onEvent:ie,onClose:()=>E(!0)}).then(C=>{I=async(P,D)=>{const N=window.__logForTest;N==null||N({method:P,params:D}),await C(P,D)},st()})},[st]),lt=f.useCallback((j,C,P,D)=>{const N=j.configFile?wt.getObject(j.configFile+":projects",void 0):void 0;for(const L of o.keys())C.suites.find(Q=>Q.title===L)||o.delete(L);for(const L of C.suites)o.has(L.title)||o.set(L.title,!!(N!=null&&N.includes(L.title)));!N&&o.size&&![...o.values()].includes(!0)&&o.set(o.entries().next().value[0],!0),p({config:j,rootSuite:C,loadErrors:P}),c(new Map(o)),_&&D?v(D):D||v(void 0)},[o,_]);const it=f.useCallback((j,C)=>{j==="bounce-if-busy"&&_||(S.current=new Set([...S.current,...C]),u.current=u.current.then(async()=>{var N,L,Q;const P=S.current;if(S.current=new Set,!P.size)return;{for(const F of((N=h.rootSuite)==null?void 0:N.allTests())||[])P.has(F.id)&&(F._clearResults(),F._createTestResult("pending"));p({...h})}const D="  ["+new Date().toLocaleTimeString()+"]";O.write("\x1B[2m—".repeat(Math.max(0,_t.cols-D.length))+D+"\x1B[22m"),v({total:0,passed:0,failed:0,skipped:0}),R({testIds:P}),await I("run",{testIds:[...P],projects:[...o].filter(([F,Et])=>Et).map(([F])=>F)});for(const F of((L=h.rootSuite)==null?void 0:L.allTests())||[])((Q=F.results[0])==null?void 0:Q.duration)===-1&&F._clearResults();p({...h}),R(void 0)}))},[o,_,h]),z=!!_,nt=f.useRef(null),kt=f.useCallback(j=>{var C;j.preventDefault(),j.stopPropagation(),(C=nt.current)==null||C.showModal()},[]),ot=f.useCallback(j=>{var C;j.preventDefault(),j.stopPropagation(),(C=nt.current)==null||C.close()},[]),jt=f.useCallback(j=>{ot(j),s(!0),I("installBrowsers").then(async()=>{s(!1);const{hasBrowsers:C}=await I("checkBrowsers");ct(C)})},[ot]);return l.jsxs("div",{className:"vbox ui-mode",children:[!J&&l.jsxs("dialog",{ref:nt,children:[l.jsxs("div",{className:"title",children:[l.jsx("span",{className:"codicon codicon-lightbulb"}),"Install browsers"]}),l.jsxs("div",{className:"body",children:["Playwright did not find installed browsers.",l.jsx("br",{}),"Would you like to run `playwright install`?",l.jsx("br",{}),l.jsx("button",{className:"button",onClick:jt,children:"Install"}),l.jsx("button",{className:"button secondary",onClick:ot,children:"Dismiss"})]})]}),k&&l.jsxs("div",{className:"disconnected",children:[l.jsx("div",{className:"title",children:"UI Mode disconnected"}),l.jsxs("div",{children:[l.jsx("a",{href:"#",onClick:()=>window.location.href="/",children:"Reload the page"})," to reconnect"]})]}),l.jsxs(Dt,{sidebarSize:250,minSidebarSize:150,orientation:"horizontal",sidebarIsFirst:!0,settingName:"testListSidebar",children:[l.jsxs("div",{className:"vbox",children:[l.jsxs("div",{className:"vbox"+(e?"":" hidden"),children:[l.jsxs(G,{children:[l.jsx("div",{className:"section-title",style:{flex:"none"},children:"Output"}),l.jsx(y,{icon:"circle-slash",title:"Clear output",onClick:()=>O.clear()}),l.jsx("div",{className:"spacer"}),l.jsx(y,{icon:"close",title:"Close",onClick:()=>s(!1)})]}),l.jsx(qt,{source:O})]}),l.jsx("div",{className:"vbox"+(e?" hidden":""),children:l.jsx(se,{item:g,rootDir:(ut=h.config)==null?void 0:ut.rootDir})})]}),l.jsxs("div",{className:"vbox ui-mode-sidebar",children:[l.jsxs(G,{noShadow:!0,noMinHeight:!0,children:[l.jsx("img",{src:"playwright-logo.svg",alt:"Playwright logo"}),l.jsx("div",{className:"section-title",children:"Playwright"}),l.jsx(y,{icon:"color-mode",title:"Toggle color mode",onClick:()=>Lt()}),l.jsx(y,{icon:"refresh",title:"Reload",onClick:()=>st(),disabled:z||T}),l.jsx(y,{icon:"terminal",title:"Toggle output",toggled:e,onClick:()=>{s(!e)}}),!J&&l.jsx(y,{icon:"lightbulb-autofix",style:{color:"var(--vscode-list-warningForeground)"},title:"Playwright browsers are missing",onClick:kt})]}),l.jsx(Gt,{filterText:n,setFilterText:t,statusFilters:i,setStatusFilters:r,projectFilters:o,setProjectFilters:c,testModel:h,runTests:()=>it("bounce-if-busy",m)}),l.jsxs(G,{noMinHeight:!0,children:[!z&&!d&&l.jsx("div",{className:"section-title",children:"Tests"}),!z&&d&&l.jsx("div",{"data-testid":"status-line",className:"status-line",children:l.jsxs("div",{children:[d.passed,"/",d.total," passed (",d.passed/d.total*100|0,"%)"]})}),z&&d&&l.jsx("div",{"data-testid":"status-line",className:"status-line",children:l.jsxs("div",{children:["Running ",d.passed,"/",_.testIds.size," passed (",d.passed/_.testIds.size*100|0,"%)"]})}),l.jsx(y,{icon:"play",title:"Run all",onClick:()=>it("bounce-if-busy",m),disabled:z||T}),l.jsx(y,{icon:"debug-stop",title:"Stop",onClick:()=>q("stop"),disabled:!z||T}),l.jsx(y,{icon:"eye",title:"Watch all",toggled:M,onClick:()=>{A({value:new Set}),X(!M)}}),l.jsx(y,{icon:"collapse-all",title:"Collapse all",onClick:()=>{b(w+1)}})]}),l.jsx(ee,{statusFilters:i,projectFilters:o,filterText:n,testModel:h,runningState:_,runTests:it,onItemSelected:a,setVisibleTestIds:x,watchAll:M,watchedTreeIds:et,setWatchedTreeIds:A,isLoading:T,requestedCollapseAllCount:w})]})]})]})},Gt=({filterText:n,setFilterText:t,statusFilters:e,setStatusFilters:s,projectFilters:i,setProjectFilters:r,testModel:o,runTests:c})=>{const[h,p]=f.useState(!1),d=f.useRef(null);f.useEffect(()=>{var a;(a=d.current)==null||a.focus()},[]);const v=[...e.entries()].filter(([a,m])=>m).map(([a])=>a).join(" ")||"all",g=[...i.entries()].filter(([a,m])=>m).map(([a])=>a).join(" ")||"all";return l.jsxs("div",{className:"filters",children:[l.jsx(Jt,{expanded:h,setExpanded:p,title:l.jsx("input",{ref:d,type:"search",placeholder:"Filter (e.g. text, @tag)",spellCheck:!1,value:n,onChange:a=>{t(a.target.value)},onKeyDown:a=>{a.key==="Enter"&&c()}})}),l.jsxs("div",{className:"filter-summary",title:"Status: "+v+`
Projects: `+g,onClick:()=>p(!h),children:[l.jsx("span",{className:"filter-label",children:"Status:"})," ",v,l.jsx("span",{className:"filter-label",children:"Projects:"})," ",g]}),h&&l.jsxs("div",{className:"hbox",style:{marginLeft:14,maxHeight:200,overflowY:"auto"},children:[l.jsx("div",{className:"filter-list",children:[...e.entries()].map(([a,m])=>l.jsx("div",{className:"filter-entry",children:l.jsxs("label",{children:[l.jsx("input",{type:"checkbox",checked:m,onClick:()=>{const x=new Map(e);x.set(a,!x.get(a)),s(x)}}),l.jsx("div",{children:a})]})}))}),l.jsx("div",{className:"filter-list",children:[...i.entries()].map(([a,m])=>l.jsx("div",{className:"filter-entry",children:l.jsxs("label",{children:[l.jsx("input",{type:"checkbox",checked:m,onClick:()=>{var B;const x=new Map(i);x.set(a,!x.get(a)),r(x);const T=(B=o==null?void 0:o.config)==null?void 0:B.configFile;T&&wt.setObject(T+":projects",[...x.entries()].filter(([_,R])=>R).map(([_])=>_))}}),l.jsx("div",{children:a||"untitled"})]})}))})]})]})},te=At,ee=({statusFilters:n,projectFilters:t,filterText:e,testModel:s,runTests:i,runningState:r,watchAll:o,watchedTreeIds:c,setWatchedTreeIds:h,isLoading:p,onItemSelected:d,setVisibleTestIds:v,requestedCollapseAllCount:g})=>{const[a,m]=f.useState({expandedItems:new Map}),[x,T]=f.useState(),[B,_]=f.useState(g),{rootItem:R,treeItemMap:M,fileNames:X}=f.useMemo(()=>{let u=re(s.rootSuite,s.loadErrors,t);le(u,e,n,r==null?void 0:r.testIds),St(u),u=ae(u),ce(u);const S=new Map,w=new Set,b=new Set,k=E=>{E.kind==="group"&&E.location.file&&b.add(E.location.file),E.kind==="case"&&E.tests.forEach(J=>w.add(J.id)),E.children.forEach(k),S.set(E.id,E)};return k(u),v(w),{rootItem:u,treeItemMap:S,fileNames:b}},[e,s,n,t,v,r]);f.useEffect(()=>{if(B!==g){a.expandedItems.clear();for(const w of M.keys())a.expandedItems.set(w,!1);_(g),T(void 0),m({...a});return}if(!r||r.itemSelectedByUser)return;let u;const S=w=>{var b;w.children.forEach(S),!u&&w.status==="failed"&&(w.kind==="test"&&r.testIds.has(w.test.id)||w.kind==="case"&&r.testIds.has((b=w.tests[0])==null?void 0:b.id))&&(u=w)};S(R),u&&T(u.id)},[r,T,R,B,_,g,a,m,M]);const{selectedTreeItem:et}=f.useMemo(()=>{const u=x?M.get(x):void 0;let S;u&&(S={file:u.location.file,line:u.location.line,source:{errors:s.loadErrors.filter(b=>{var k;return((k=b.location)==null?void 0:k.file)===u.location.file}).map(b=>({line:b.location.line,message:b.message})),content:void 0}});let w;return(u==null?void 0:u.kind)==="test"?w=u.test:(u==null?void 0:u.kind)==="case"&&u.tests.length===1&&(w=u.tests[0]),d({treeItem:u,testCase:w,testFile:S}),{selectedTreeItem:u}},[d,x,s,M]);f.useEffect(()=>{if(!p)if(o)q("watch",{fileNames:[...X]});else{const u=new Set;for(const S of c.value){const w=M.get(S),b=w==null?void 0:w.location.file;b&&u.add(b)}q("watch",{fileNames:[...u]})}},[p,R,X,o,c,M]);const A=u=>{T(u.id),i("bounce-if-busy",rt(u))};return vt=u=>{const S=[],w=new Set(u);if(o){const b=k=>{const E=k.location.file;E&&w.has(E)&&S.push(...rt(k)),k.kind==="group"&&k.subKind==="folder"&&k.children.forEach(b)};b(R)}else for(const b of c.value){const k=M.get(b),E=k==null?void 0:k.location.file;E&&w.has(E)&&S.push(...rt(k))}i("queue-if-busy",new Set(S))},l.jsx(te,{name:"tests",treeState:a,setTreeState:m,rootItem:R,dataTestId:"test-tree",render:u=>l.jsxs("div",{className:"hbox ui-mode-list-item",children:[l.jsx("div",{className:"ui-mode-list-item-title",title:u.title,children:u.title}),!!u.duration&&u.status!=="skipped"&&l.jsx("div",{className:"ui-mode-list-item-time",children:Ft(u.duration)}),l.jsxs(G,{noMinHeight:!0,noShadow:!0,children:[l.jsx(y,{icon:"play",title:"Run",onClick:()=>A(u),disabled:!!r}),l.jsx(y,{icon:"go-to-file",title:"Open in VS Code",onClick:()=>q("open",{location:oe(u)}),style:u.kind==="group"&&u.subKind==="folder"?{visibility:"hidden"}:{}}),!o&&l.jsx(y,{icon:"eye",title:"Watch",onClick:()=>{c.value.has(u.id)?c.value.delete(u.id):c.value.add(u.id),h({...c})},toggled:c.value.has(u.id)})]})]}),icon:u=>It(u.status),selectedItem:et,onAccepted:A,onSelected:u=>{r&&(r.itemSelectedByUser=!0),T(u.id)},isError:u=>u.kind==="group"?u.hasLoadErrors:!1,autoExpandDepth:e?5:1,noItemsMessage:p?"Loading…":"No tests"})},se=({item:n,rootDir:t})=>{var g;const[e,s]=f.useState(),[i,r]=f.useState(0),o=f.useRef(null),{outputDir:c}=f.useMemo(()=>({outputDir:n.testCase?ne(n.testCase):void 0}),[n]),[h,p]=f.useState(),d=f.useCallback(a=>p(dt(a)),[p]),v=h?e==null?void 0:e.model.actions.find(a=>dt(a)===h):void 0;return f.useEffect(()=>{var T,B;o.current&&clearTimeout(o.current);const a=(T=n.testCase)==null?void 0:T.results[0];if(!a){s(void 0);return}const m=a&&a.duration>=0&&a.attachments.find(_=>_.name==="trace");if(m&&m.path){mt(m.path).then(_=>s({model:_,isLive:!1}));return}if(!c){s(void 0);return}const x=`${c}/${Qt(a.workerIndex)}/traces/${(B=n.testCase)==null?void 0:B.id}.json`;return o.current=setTimeout(async()=>{try{const _=await mt(x);s({model:_,isLive:!0})}catch{s(void 0)}finally{r(i+1)}},500),()=>{o.current&&clearTimeout(o.current)}},[c,n,s,i,r]),l.jsx(Wt,{model:e==null?void 0:e.model,hideStackFrames:!0,showSourcesFirst:!0,rootDir:t,initialSelection:v,onSelectionChanged:d,fallbackLocation:n.testFile,isLive:e==null?void 0:e.isLive,status:(g=n.treeItem)==null?void 0:g.status},"workbench")};let H,$,pt,tt,U;const gt=()=>{clearTimeout(tt),tt=void 0,lt(U.config,U.rootSuite,U.loadErrors,U.progress)},K=(n,t,e,s,i=!1)=>{U={config:n,rootSuite:t,loadErrors:e,progress:s},i?gt():tt||(tt=setTimeout(gt,250))},bt=n=>{if(!n)return I("list",{});let t;const e=[],s={total:0,passed:0,failed:0,skipped:0};let i;return H=new ft(Y,{version:()=>"v2",onConfigure:r=>{i=r,$=new ft(Y,{onBegin:o=>{pt=o.allTests().length,$=void 0}},!1)},onBegin:r=>{t||(t=r),s.total=pt,s.passed=0,s.failed=0,s.skipped=0,K(i,t,e,s,!0)},onEnd:()=>{K(i,t,e,s,!0)},onTestBegin:()=>{K(i,t,e,s)},onTestEnd:r=>{r.outcome()==="skipped"?++s.skipped:r.outcome()==="unexpected"?++s.failed:++s.passed,K(i,t,e,s)},onError:r=>{O.write((r.stack||r.value||"")+`
`),e.push(r),K(i,t??new W("","root"),e,s)},printsToStdio:()=>!1,onStdOut:()=>{},onStdErr:()=>{},onExit:()=>{},onStepBegin:()=>{},onStepEnd:()=>{}},!0),H._setClearPreviousResultsWhenTestBegins(),I("list",{})},q=(n,t)=>{if(window._overrideProtocolForTest){window._overrideProtocolForTest({method:n,params:t}).catch(()=>{});return}I(n,t).catch(e=>{console.error(e)})},ie=(n,t)=>{var e,s;if(n==="listChanged"){bt(!1).catch(()=>{});return}if(n==="testFilesChanged"){vt(t.testFileNames);return}if(n==="stdio"){if(t.buffer){const i=atob(t.buffer);O.write(i)}else O.write(t.text);return}(e=$==null?void 0:$.dispatch({method:n,params:t}))==null||e.catch(()=>{}),(s=H==null?void 0:H.dispatch({method:n,params:t}))==null||s.catch(()=>{})},ne=n=>{var t;for(let e=n.parent;e;e=e.parent)if(e.project())return(t=e.project())==null?void 0:t.outputDir},oe=n=>{if(n)return n.location.file+":"+n.location.line},rt=n=>{const t=new Set;if(!n)return t;const e=s=>{var i;s.kind==="case"?s.tests.map(r=>r.id).forEach(r=>t.add(r)):s.kind==="test"?t.add(s.id):(i=s.children)==null||i.forEach(e)};return e(n),t};function at(n,t,e,s){if(t.length===0)return n;const i=t.join(Y),r=s.get(i);if(r)return r;const o=at(n,t.slice(0,t.length-1),!1,s),c={kind:"group",subKind:e?"file":"folder",id:i,title:t[t.length-1],location:{file:i,line:0,column:0},duration:0,parent:o,children:[],status:"none",hasLoadErrors:!1};return o.children.push(c),s.set(i,c),c}function re(n,t,e){const s=[...e.values()].some(Boolean),i={kind:"group",subKind:"folder",id:"root",title:"",location:{file:"",line:0,column:0},duration:0,parent:void 0,children:[],status:"none",hasLoadErrors:!1},r=(c,h,p)=>{for(const d of h.suites){const v=d.title||"<anonymous>";let g=p.children.find(a=>a.kind==="group"&&a.title===v);g||(g={kind:"group",subKind:"describe",id:"suite:"+h.titlePath().join("")+""+v,title:v,location:d.location,duration:0,parent:p,children:[],status:"none",hasLoadErrors:!1},p.children.push(g)),r(c,d,g)}for(const d of h.tests){const v=d.title;let g=p.children.find(x=>x.kind!=="group"&&x.title===v);g||(g={kind:"case",id:"test:"+d.titlePath().join(""),title:v,parent:p,children:[],tests:[],location:d.location,duration:0,status:"none"},p.children.push(g));const a=d.results[0];let m="none";(a==null?void 0:a.statusEx)==="scheduled"?m="scheduled":(a==null?void 0:a.statusEx)==="running"?m="running":(a==null?void 0:a.status)==="skipped"?m="skipped":(a==null?void 0:a.status)==="interrupted"?m="none":a&&d.outcome()!=="expected"?m="failed":a&&d.outcome()==="expected"&&(m="passed"),g.tests.push(d),g.children.push({kind:"test",id:d.id,title:c,location:d.location,test:d,parent:g,children:[],status:m,duration:d.results.length?Math.max(0,d.results[0].duration):0,project:c}),g.duration=g.children.reduce((x,T)=>x+T.duration,0)}},o=new Map;for(const c of(n==null?void 0:n.suites)||[])if(!(s&&!e.get(c.title)))for(const h of c.suites){const p=at(i,h.location.file.split(Y),!0,o);r(c.title,h,p)}for(const c of t){if(!c.location)continue;const h=at(i,c.location.file.split(Y),!0,o);h.hasLoadErrors=!0}return i}function le(n,t,e,s){const i=t.trim().toLowerCase().split(" "),r=[...e.values()].some(Boolean),o=h=>{const p=h.tests[0].titlePath().join(" ").toLowerCase();return!i.every(d=>p.includes(d))&&!h.tests.some(d=>s==null?void 0:s.has(d.id))?!1:(h.children=h.children.filter(d=>!r||(s==null?void 0:s.has(d.test.id))||e.get(d.status)),h.tests=h.children.map(d=>d.test),!!h.children.length)},c=h=>{const p=[];for(const d of h.children)d.kind==="case"?o(d)&&p.push(d):(c(d),(d.children.length||d.hasLoadErrors)&&p.push(d));h.children=p};c(n)}function St(n){for(const o of n.children)St(o);n.kind==="group"&&n.children.sort((o,c)=>o.location.file.localeCompare(c.location.file)||o.location.line-c.location.line);let t=n.children.length>0,e=n.children.length>0,s=!1,i=!1,r=!1;for(const o of n.children)e=e&&o.status==="skipped",t=t&&(o.status==="passed"||o.status==="skipped"),s=s||o.status==="failed",i=i||o.status==="running",r=r||o.status==="scheduled";i?n.status="running":r?n.status="scheduled":s?n.status="failed":e?n.status="skipped":t&&(n.status="passed")}function ae(n){let t=n;for(;t.children.length===1&&t.children[0].kind==="group"&&t.children[0].subKind==="folder";)t=t.children[0];return t.location=n.location,t}function ce(n){const t=e=>{e.kind==="case"&&e.children.length===1?e.children=[]:e.children.forEach(t)};t(n)}async function mt(n){const t=new URLSearchParams;t.set("trace",n);const s=await(await fetch(`contexts?${t.toString()}`)).json();return new Ot(s)}const Y=navigator.userAgent.toLowerCase().includes("windows")?"\\":"/";(async()=>{if(zt(),window.location.protocol!=="file:"){if(window.location.href.includes("isUnderTest=true")&&await new Promise(n=>setTimeout(n,1e3)),!navigator.serviceWorker)throw new Error(`Service workers are not supported.
Make sure to serve the website (${window.location}) via HTTPS or localhost.`);navigator.serviceWorker.register("sw.bundle.js"),navigator.serviceWorker.controller||await new Promise(n=>{navigator.serviceWorker.oncontrollerchange=()=>n()}),setInterval(function(){fetch("ping")},1e4)}Vt.render(l.jsx(Zt,{}),document.querySelector("#root"))})();
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["./assets/xtermModule-Yt6xwiJ_.js","./xtermModule.0lwXJFHT.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
