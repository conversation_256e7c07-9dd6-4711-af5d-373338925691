import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true
  }
})

test.use({ storageState: CONST.authFile })

test('Process Model Edit', async ({ page }) => {
  test.setTimeout(480000)

  await page.setViewportSize({ width: 3000, height: 1080 });

  await page.goto(`${CONST.baseURL}/products`)

  await page.getByRole('cell', { name: 'Acme Office Chair', exact: true }).click();
  await page.getByRole('tab', { name: 'Process Model' }).click();

  await page.waitForTimeout(2000);

  await page.hover('div#material-Wood-\\(Oak-Wood\\)');

  await page.click('button#add-node-btn');

  await page.waitForSelector(
    `text=Add Connected Node`,
    { timeout: 2000 }
  )

  await page.locator('form#add-node-form').getByPlaceholder('Citric Acid').fill('Steel Rod')

  await page.locator('form#add-node-form').getByPlaceholder('150.50').fill('1500.50')

  await expect(page.getByRole('dialog')).toContainText('Los Angeles, United States', { timeout: 10000 });

  await expect(page.getByRole('dialog')).toContainText('reinforcing steel production', { timeout: 85000 });

  await page.getByRole('button', { name: 'Add Node' }).click();

  await page.evaluate(() => {
    document.getElementById('material-Steel-Rod')?.click();
  });

  await page.waitForSelector(
    `text=Node Details: Steel Rod`,
    { timeout: 10000 }
  )

  await page.keyboard.press('Escape');

  await page.getByRole('button', { name: 'Save Changes' }).click();

  await expect(page.locator('body')).toContainText('Steel Rod (material) requires at least 1 outgoing connections');

  await page.hover('div#material-Steel-Rod');

  await page.click('button#delete-node-btn');

  await page.hover('div#material-Wood-\\(Oak-Wood\\)');

  await page.click('button#edit-node-btn');

  await page.waitForSelector(
    `text=Edit Node: Wood (Oak Wood)`,
    { timeout: 10000 }
  )

  await page.locator('form#edit-node-form').getByPlaceholder('Citric Acid').fill('Citric Acid')

  await page.locator('form#edit-node-form').getByPlaceholder('150.50').fill('100.50')

  await expect(page.getByRole('dialog')).toContainText('citric acid production', { timeout: 85000 });

  await page.getByRole('button', { name: 'Update' }).click();

  await page.waitForSelector(
    `text=Node updated successfully`,
    { timeout: 10000 }
  )

  await page.evaluate(() => {
    document.getElementById('material-Citric-Acid')?.click();
  });

  await page.waitForSelector(
    `text=Node Details: Citric Acid`,
    { timeout: 2000 }
  )

  await page.keyboard.press('Escape');

  await page.waitForTimeout(2000);

  await page.hover('div#use-Consumer-Use');

  await page.click('button#edit-node-btn');

  await page.waitForSelector(
    `text=Edit Node: Consumer Use`,
    { timeout: 10000 }
  )

  await page.locator('form#edit-node-form #quantity').fill('10')

  await page.getByRole('button', { name: 'Update' }).click();

  await page.waitForSelector(
    `text=Node updated successfully`,
    { timeout: 10000 }
  )

  await page.getByRole('button', { name: 'Save Changes' }).click();

  await page.waitForSelector(
    `text=Acme Office Chair updated successfully`,
    { timeout: 420000 }
  )

})