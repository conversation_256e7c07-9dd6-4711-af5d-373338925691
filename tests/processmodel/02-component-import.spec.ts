import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true
  }
})

test.use({ storageState: CONST.authFile })

test('Add Component From File', async ({ page }) => {
  test.setTimeout(420000)
  await page.goto(`${CONST.baseURL}/component/import`)
  await page.waitForSelector('text=Upload File', { timeout: 25000 })
  await page.context().storageState({ path: CONST.authFile })

  page.on('response', async (response) => {
    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json()
        console.log('GraphQL Response:', JSON.stringify(responseBody))
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  const input = await page.$('input[type="file"]')

  await input?.setInputFiles('tests/files/components-list.csv')

  await page.waitForSelector(
    'text=Are you sure you want to upload this file ?',
    { timeout: 60000 }
  )

  await page.click('button:has-text("Confirm")')

  await page.waitForSelector('text=Side Bar Left', { timeout: 320000 })

  await page.getByPlaceholder('Elegant Office Chair').click();
  await page.getByPlaceholder('Elegant Office Chair').fill('Acme Office Chair');
  await page.getByRole('button', { name: 'Save' }).click();

  await page.waitForSelector(
    `text=Components created successfully`,
    { timeout: 260000 }
  )

  await page.waitForURL(`${CONST.baseURL}/component`)
})