import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true
  }
})

test.use({ storageState: CONST.authFile })

test('Basic Product Edit', async ({ page }) => {
  test.setTimeout(480000)

  await page.setViewportSize({ width: 3000, height: 1080 });

  await page.goto(`${CONST.baseURL}/products`)

  await page.getByRole('cell', { name: 'Acme Office Chair', exact: true }).click();
  await page.getByRole('tab', { name: 'Product Details' }).click();
  await page.getByRole('button', { name: 'right Product Details edit' }).getByRole('button').click();
  await page.getByPlaceholder('Ariel Laundry Liquid').fill('Acme Office Chair123');
  await page.locator('#target-market-location-clear-icon svg').click();
  await page.getByLabel('Target Market/Region').fill('germ');
  await page.getByTitle('Germany').click();
  await page.getByPlaceholder('#').fill('173643825586235');
  await page.getByPlaceholder('25000').fill('100');
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForSelector(
    `text=Product Acme Office Chair123 updated successfully`,
    { timeout: 420000 }
  )

})

test('Verify Basic Product Edit', async ({ page }) => {
  test.setTimeout(220000)

  await page.setViewportSize({ width: 3000, height: 1080 });

  await page.goto(`${CONST.baseURL}/products`)

  await page.getByRole('cell', { name: 'Acme Office Chair123', exact: true }).click();

  await expect(page.locator('#product-info-card')).toContainText('Germany');
  await expect(page.locator('#product-info-card')).toContainText('173643825586235');

})