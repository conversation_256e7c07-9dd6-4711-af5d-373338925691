import { test, expect } from '@playwright/test'
import { CONST } from '../const'
const fs = require('fs')
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
})

test.use({ storageState: CONST.authFile })

test('Update Tag', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible({ timeout: 20000 })

  await page.locator('span.add-tag-button').nth(1).click()

  await page.locator('.tag-input').click()
  await page.locator('.tag-input').fill('Tagging Laundry Liquid')
  await page.locator('.tag-input').press('Enter')

  await page.waitForSelector(`text=Tagging Laundry Liquid`, { timeout: 60000 })
})

test('Verify Updated Tag', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible({ timeout: 20000 })

  await page.waitForSelector(`text=Tagging Laundry Liquid`, { timeout: 10000 })
})

test('Filter By Tag', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible({ timeout: 20000 })

  await page.getByRole('button', { name: 'filter' }).click()
  await page
    .getByRole('menuitem', { name: 'Tagging Laundry Liquid' })
    .locator('span')
    .first()
    .click()
  await page.getByRole('button', { name: 'OK' }).click()

  await page.waitForSelector(`text=Tagging Laundry Liquid`, { timeout: 10000 })

  const productCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(productCount).toBe(1)
})
