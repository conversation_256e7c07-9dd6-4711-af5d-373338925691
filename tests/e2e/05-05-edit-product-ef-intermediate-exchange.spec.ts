//import { test, expect } from '@playwright/test'
//import { CONST } from '../const'
//import state from '../state'

//test.describe.serial('Edit Product: Add/Edit/Delete Intermediate Exchange Tests', () => {

  //test.beforeEach(() => {
    //if (state.hasFailed) {
      //test.skip(true, 'Skipping due to a previous test failure')
    //}
  //})

  //test.afterEach(async ({ page }, testInfo) => {
    //if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
      //state.hasFailed = true
    //}
  //})

  //test.use({ storageState: CONST.authFile })

  // test('Add/Edit/Delete EF Intermediate Exchanges', async ({ page }) => {
  //   test.setTimeout(220000)
  //   await page.goto(`${CONST.baseURL}/products`)
  //   await page.waitForSelector('text=Product Name')
  //   await page.context().storageState({ path: CONST.authFile })

    //await page
      //.locator('input#search-input')
      //.pressSequentially('Vibrant Colors Laundry Plus', { delay: 100 })

    //const productInfo = page
      //.locator('table tr.ant-table-row.ant-table-row-level-0')
      //.first()

    //expect(productInfo).toBeVisible()

    //await productInfo.locator('td:nth-child(3)').click()

    //await page.waitForSelector(`text=Vibrant Colors Laundry Plus`)

    //await page.getByRole('tab', { name: 'Product Details' }).click()

    //page.on('response', async (response) => {
      //if (response.url().includes('/graphql')) {
        //try {
          //const responseBody = await response.json();
          //console.log('GraphQL Response:', JSON.stringify(responseBody));
        //} catch (error) {
          //console.error('Error parsing response:', error)
        //}
      //}
    //})

    //await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click();
    //await page.getByRole('button', { name: 'right Raw Materials edit Edit' }).getByRole('button').click();
    //await expect(page.getByRole('cell', { name: 'citric acid production AI edit' }).getByRole('button')).toBeVisible({ timeout: 60000 });
    //await page.getByRole('cell', { name: 'citric acid production AI edit' }).getByRole('button').click();

    //await expect(page.getByRole('row', { name: 'citric acid production maize starch RoW HIGH The activity \'citric acid' }).getByRole('button')).toBeVisible({ timeout: 20000 });
    //await page.getByRole('row', { name: 'citric acid production maize starch RoW HIGH The activity \'citric acid' }).getByRole('button').click();

    ////Add Intermediate Exchange
    //await page.getByRole('button', { name: 'plus Add Intermediate Exchange' }).click();
    //await page.getByPlaceholder('Enter material or process (e.').click();
    //await page.getByPlaceholder('Enter material or process (e.').fill('wastewater');
    //await page.click('button#search-btn')

    //await expect(page.getByText('direct disposal of wastewater')).toBeVisible({ timeout: 20000 });
    //await page.getByRole('row', { name: 'direct disposal of wastewater' }).getByLabel('').check();
    //await page.getByLabel('Add Exchange').getByRole('button', { name: 'Next' }).click();
    //await page.getByPlaceholder('eg: Electricity, Wastewater,').click();
    //await page.getByPlaceholder('eg: Electricity, Wastewater,').fill('wastewater');
    //await page.getByPlaceholder('0.122').click();
    //await page.getByPlaceholder('0.122').fill('0.50');
    //await page.getByLabel('Add Exchange').getByRole('button', { name: 'Next' }).click();
    //await page.click('button#add-exchange-submit')

    //await page.waitForSelector(
      //`text=Exchange created successfully`,
      //{ timeout: 10000 }
    //)

    //await expect(page.getByRole('cell', { name: 'wastewater' }).first()).toBeVisible({ timeout: 20000 });
    //await expect(page.getByRole('cell', { name: 'direct disposal of wastewater' }).first()).toBeVisible({ timeout: 20000 });

    ////Edit Intermediate Exchange
    //await page.getByLabel('Exchanges').getByRole('button', { name: 'edit' }).first().click();
    //await page.getByPlaceholder('Enter material or process (e.').click();
    //await page.getByPlaceholder('Enter material or process (e.').fill('casting');
    //await page.click('button#search-btn')
    //await page.getByRole('row', { name: 'aluminium casting facility construction - RoW' }).getByLabel('').check();
    //await page.getByLabel('Edit Exchange').getByRole('button', { name: 'Next' }).click();
    //await page.getByPlaceholder('0.122').dblclick();
    //await page.getByPlaceholder('0.122').fill('15');
    //await page.getByPlaceholder('eg: Electricity, Wastewater,').click();
    //await page.getByPlaceholder('eg: Electricity, Wastewater,').fill('aluminium casting');
    //await page.getByLabel('Edit Exchange').getByRole('button', { name: 'Next' }).click();
    //await page.click('button#add-exchange-submit')
    //await page.waitForSelector(
      //`text=Exchange updated successfully`,
      //{ timeout: 10000 }
    //)
    //await expect(page.getByRole('cell', { name: 'aluminium casting' }).first()).toBeVisible({ timeout: 20000 });
    //await expect(page.getByRole('cell', { name: 'aluminium casting facility' }).first()).toBeVisible({ timeout: 20000 });

    ////Delete Intermediate Exchange
    //await page.getByRole('button', { name: 'delete', exact: true }).first().click();
    //await page.getByRole('button', { name: 'OK' }).click();
    //await page.waitForSelector(
      //`text=Exchange deleted successfully`,
      //{ timeout: 10000 }
    //)
  //})

//})
