import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure');
  }
});

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
});

test.use({ storageState: CONST.authFile })

test('Dashboard Metrics', async ({ page }) => {
  test.setTimeout(620000)
  await page.goto(`${CONST.baseURL}/dashboard`)
  await page.waitForSelector('text=Total Product Emissions')
  await page.context().storageState({ path: CONST.authFile })

  try {
    const totalProductEmissionsText =
      (await page
        .locator(
          'xpath=/html/body/div/div/div/main/div/main/div/main/div[1]/div[1]/div/p[2]'
        )
        .textContent()) ?? ''

    const totalProductEmissionsValue = parseFloat(
      totalProductEmissionsText.trim().replace('kg CO2e', '')
    )

    expect(typeof totalProductEmissionsValue).toBe('number')

    const totalProductSKUText =
      (await page
        .locator(
          'xpath=/html/body/div/div/div/main/div/main/div/main/div[1]/div[3]/div/p[2]'
        )
        .textContent()) ?? ''

    const totalProductSKU = parseFloat(
      totalProductSKUText.trim().replace("SKU's", '')
    )

    expect(typeof totalProductSKU).toBe('number')
  } catch (error) {
    console.error('Error running test:', error.message)
  }
})
