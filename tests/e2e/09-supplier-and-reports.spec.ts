import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
})

test.use({ storageState: CONST.authFile })

test('Supplier Count', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/suppliers`)
  await page.waitForSelector('text=Supplier Name', { timeout: 120000 })
  await page.context().storageState({ path: CONST.authFile })

  const supplierCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(supplierCount).toBeGreaterThanOrEqual(1)
})

test('Verify Created Supplier', async ({ page }) => {
  test.setTimeout(60000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible()

  await productInfo.locator('td:nth-child(3)').click()

  await page.waitForSelector(`text=${CONST.testProduct.productName}`)

  await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click()

  const rawMaterialInfo = page
    .locator(
      'div#rawMaterialsIngredients table tr.ant-table-row.ant-table-row-level-0'
    )
    .first()

  const supplierName =
    (await rawMaterialInfo.locator('td:nth-child(8) a').textContent()) ?? ''

  await rawMaterialInfo.locator('td:nth-child(8) a').click()

  await page.waitForSelector(`text=${supplierName}`)

  await expect(page.getByText('Raw Material')).toBeVisible()

  await page.getByRole('tab', { name: 'Products' }).click()

  const purchasedInventoryCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(purchasedInventoryCount).toBeGreaterThanOrEqual(1)

  const purchasedInventoryProductInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  const purchasedInventoryProductName =
    (await purchasedInventoryProductInfo
      .locator('td:nth-child(2) a')
      .textContent()) ?? ''

  expect(purchasedInventoryProductName).toEqual(CONST.testProduct.productName)
})

test('Product Inventory Summary', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/reports`)
  await page.waitForSelector('text=Product Inventory Summary')
  await page.context().storageState({ path: CONST.authFile })

  page.on('response', async (response) => {
    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json();
        console.log('GraphQL Response:', JSON.stringify(responseBody));
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page.getByRole('img', { name: 'Product Inventory Summary' }).click()
  await page.waitForSelector('text=Brand', { timeout: 120000 })

  const productCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(productCount).toBeGreaterThanOrEqual(1)

  const downloadPromise = page.waitForEvent('download')
  await page.getByRole('button', { name: 'download' }).click()
  const download = await downloadPromise
  expect(download.suggestedFilename()).toEqual(
    'CarbonBright_Product_Inventory_Summary_Report.csv'
  )

  await page.getByRole('button', { name: 'filter' }).click()
  await page
    .getByRole('menuitem', { name: 'Tagging Laundry Liquid' })
    .locator('span')
    .first()
    .click()
  await page.getByRole('button', { name: 'OK' }).click()

  await page.waitForSelector(`text=Tagging Laundry Liquid`, { timeout: 5000 })

  const filteredProductCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(filteredProductCount).toBe(1)
})

test('Product Emissions By Category', async ({ page }) => {
  test.setTimeout(120000)
  await page.goto(`${CONST.baseURL}/reports`)
  await page.waitForSelector('text=Product Emissions by Category')
  await page.context().storageState({ path: CONST.authFile })

  await page.getByRole('img', { name: 'Product Emissions by Category' }).click()
  await page.waitForSelector('text=Total (kg CO2e)')

  const productCategoryCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(productCategoryCount).toBeGreaterThanOrEqual(1)

  const categoryEmissionsBarChart = page.locator(
    'div[data-chart-source-type="G2Plot"]'
  )

  await expect(categoryEmissionsBarChart).toBeVisible()

  const downloadPromise = page.waitForEvent('download')
  await page.getByRole('button', { name: 'download' }).click()
  const download = await downloadPromise
  expect(download.suggestedFilename()).toEqual(
    'CarbonBright_Product_Emissions_By_Category_Report.csv'
  )
})

test('Supplier Emissions Summary', async ({ page }) => {
  test.setTimeout(120000)
  await page.goto(`${CONST.baseURL}/reports`)
  await page.waitForSelector('text=Supplier Emissions Summary')
  await page.context().storageState({ path: CONST.authFile })

  await page.getByRole('img', { name: 'Supplier Emissions Summary' }).click()
  await page.waitForSelector('text=Scope 3 Emissions (kg CO2e)', { timeout: 60000 })

  const supplierCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(supplierCount).toBeGreaterThanOrEqual(1)

  const supplierProductEmissionsBarChart = page.locator(
    'div[data-chart-source-type="G2Plot"]'
  )

  await expect(supplierProductEmissionsBarChart).toBeVisible()

  const downloadPromise = page.waitForEvent('download')
  await page.getByRole('button', { name: 'download' }).click()
  const download = await downloadPromise
  expect(download.suggestedFilename()).toEqual(
    'CarbonBright_Product_Emissions_By_Supplier_Report.csv'
  )
})
