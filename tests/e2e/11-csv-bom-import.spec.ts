import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true
  }
})

test.use({ storageState: CONST.authFile })

test('Import Product from CSV BOM', async ({ page }) => {
  test.setTimeout(120000) // 2 minutes timeout for CSV processing

  await page.goto(`${CONST.baseURL}/product/import`)
  await page.waitForSelector('text=Upload File', { timeout: 25000 })

  // Create a test CSV file content
  const csvContent = `Ingredient,Function,Total Quantity (kg)
Water (deionized),Base/Solvent,25
Hyaluronic Acid,Hydration/Plumping Agent,2.5
Niacinami<PERSON>,Brightening/Anti-Aging Agent,2
<PERSON><PERSON><PERSON>,<PERSON>,0.5
<PERSON>,<PERSON><PERSON><PERSON><PERSON>,5`

  // Create a blob and set it as file input
  await page.evaluate((content) => {
    const blob = new Blob([content], { type: 'text/csv' })
    const file = new File([blob], 'test-bom.csv', { type: 'text/csv' })
    
    // Trigger file input
    const input = document.querySelector('input[type="file"]') as HTMLInputElement
    if (input) {
      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)
      input.files = dataTransfer.files
      input.dispatchEvent(new Event('change', { bubbles: true }))
    }
  }, csvContent)

  // Wait for file preview
  await page.waitForSelector('text=Are you sure you want to upload this file ?', { timeout: 10000 })
  
  // Confirm upload
  await page.click('button:has-text("Confirm")')

  // Wait for CSV processing to complete
  await page.waitForSelector('text=CSV Import Successful', { timeout: 60000 })

  // Verify success message and instructions
  await expect(page.locator('text=Please review the imported materials and complete the product creation wizard.')).toBeVisible()

  // Verify we're still on the product info step (step 0) like PDF/Excel import
  await expect(page.locator('input[placeholder="Product Name"]')).toBeVisible()
  await expect(page.locator('input[placeholder="Product Name"]')).toHaveValue('test-bom')
  
  // Verify ingredients table has the imported data
  await expect(page.locator('table').filter({ hasText: 'Water (deionized)' })).toBeVisible()
  await expect(page.locator('table').filter({ hasText: 'Hyaluronic Acid' })).toBeVisible()
  
  // Verify the wizard is still active and we can proceed
  await expect(page.locator('button:has-text("Next")')).toBeVisible()
})