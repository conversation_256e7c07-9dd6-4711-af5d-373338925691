import { test, expect, Request } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure');
  }
});

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
});

test.use({ storageState: CONST.authFile })

const requestTimings = new Map<Request, number>();

test('Add Product', async ({ page }) => {
  test.setTimeout(680000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page.click('button#add-product-dropdown-button')
  await page.click('button#add-single-product-button')

  page.on('request', (request) => {
    requestTimings.set(request, Date.now());
  });

  page.on('console', msg => {
    console.log(`[Console ${msg.type()}] ${msg.text()}`);
  });

  page.on('response', async (response) => {
    console.log('Response URL:', response.url());
    console.log('Response Status:', response.status());

    const request = response.request();
    const startTime = requestTimings.get(request);
    if (startTime) {
      const latency = Date.now() - startTime;
      console.log(`Response Time: ${latency}ms`);
      requestTimings.delete(request);
    }

    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json();
        console.log('GraphQL Response:', JSON.stringify(responseBody));
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page.screenshot({
    path: 'tests/screenshots/add_product_page_loading.png',
  })

  await page.waitForSelector('text=Product Info', { timeout: 25000 });

  await page.screenshot({
    path: 'tests/screenshots/add_product_page_loading_1.png',
  })

  //Product Info
  await page
    .locator('input#productName')
    .pressSequentially(CONST.testProduct.productName, { delay: 500 })


  // await page.click('button:has-text("Use Copilot")')
  await page
    .locator('input#category')
    .pressSequentially(
      CONST.testProduct.category,
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.category}")`
  )

  await page
    .locator('input#factoryLocation')
    .pressSequentially(
      CONST.testProduct.mapboxAutocompleteInputs[0].searchKeyword,
      { delay: 100 }
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.mapboxAutocompleteInputs[0].searchResult}")`
  )

  await page
    .locator('input#targetMarketLocation')
    .pressSequentially(
      CONST.testProduct.mapboxAutocompleteInputs[1].searchKeyword,
      { delay: 100 }
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.mapboxAutocompleteInputs[1].searchResult}")`
  )

  await page.getByRole('button', { name: 'right Optional Data' }).click();

  await page.getByPlaceholder('1.5L').fill('1L');

  await page.click('button:has-text("Next")')

  //Ingredients
  //Ingredient 1
  await page.click('button#add-ingredient-button')

  await page.fill(
    'form[id="add-ingredient-form"] input#ingredient',
    CONST.testProduct.ingredients[0].name
  )
  await page.fill(
    'form[id="add-ingredient-form"] input#weight',
    CONST.testProduct.ingredients[0].weight
  )

  await page.click('button#add-ingredient-submit-button')

  await page.waitForTimeout(1000)

  //Ingredient 2
  await page.click('button#add-ingredient-button')

  await page.fill(
    'form[id="add-ingredient-form"] input#ingredient',
    CONST.testProduct.ingredients[1].name
  )
  await page.fill(
    'form[id="add-ingredient-form"] input#weight',
    CONST.testProduct.ingredients[1].weight
  )

  await page.click('input#advanced-ingredient-fields-checkbox')

  await page.fill(
    'form[id="add-ingredient-form"] input#supplierName',
    CONST.testProduct.ingredients[1].supplierName ?? ''
  )
  await page
    .locator('form[id="add-ingredient-form"] input#supplierOrigin')
    .pressSequentially(
      CONST.testProduct.ingredients[1].mapboxAutocompleteInputs
        ?.searchKeyword ?? '',
      { delay: 100 }
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.ingredients[1].mapboxAutocompleteInputs?.searchResult}")`
  )

  await page.click('button#add-ingredient-submit-button')

  await page.waitForTimeout(1000)

  //Ingredient 3
  await page.click('button#add-ingredient-button')

  await page.fill(
    'form[id="add-ingredient-form"] input#ingredient',
    CONST.testProduct.ingredients[2].name
  )
  await page.fill(
    'form[id="add-ingredient-form"] input#weight',
    CONST.testProduct.ingredients[2].weight
  )

  await page.click('button#add-ingredient-submit-button')

  await page.waitForTimeout(1000)

  //Ingredient Source Prediction
  const ingredient1SourceSelector =
    '#add-ingredient-table table tbody tr:nth-child(2) td:nth-child(5)'
  await page.screenshot({
    path: 'tests/screenshots/before_waiting_for_source_ingredient1.png',
  })
  // Wait until the source country is predicted
  await page.waitForFunction(
    (ingredient1SourceSelector) => {
      const cell = document.querySelector(ingredient1SourceSelector)
      return cell && cell.textContent?.trim().includes('AI')
    },
    ingredient1SourceSelector,
    { timeout: 80000 }
  )
  await page.screenshot({
    path: 'tests/screenshots/after_waiting_for_source_ingredient1.png',
  })

  const ingredient2SourceSelector =
    '#add-ingredient-table table tbody tr:nth-child(4) td:nth-child(5)'
  await page.screenshot({
    path: 'tests/screenshots/before_waiting_for_source_ingredient2.png',
  })
  // Wait until the source country is predicted
  await page.waitForFunction(
    (ingredient2SourceSelector) => {
      const cell = document.querySelector(ingredient2SourceSelector)
      return cell && cell.textContent?.trim().includes('AI')
    },
    ingredient2SourceSelector,
    { timeout: 80000 }
  )
  await page.screenshot({
    path: 'tests/screenshots/after_waiting_for_source_ingredient2.png',
  })

  //Edit Ingredient
  await page.getByRole('button', { name: 'Edit', exact: true }).nth(1).click();
  await page.getByPlaceholder('120').click();
  await page.getByPlaceholder('120').fill('52.25');
  await page.getByRole('button', { name: 'Add', exact: true }).click();
  await page.waitForSelector('text=52.25 g');

  //Delete Ingredient
  await page.getByRole('button', { name: 'Delete' }).nth(1).click();
  await page.getByRole('button', { name: 'OK' }).click();

  for (const rawMaterial of CONST.testProduct.ingredients.slice(3)) {
    await page.waitForTimeout(1000);
    await page.getByRole('button', { name: 'plus-circle Add Raw Material' }).click();
    await page.getByLabel('Raw Material Name').click();
    await page.getByLabel('Raw Material Name').fill(rawMaterial.name);
    await page.getByTitle(rawMaterial.name, { exact: true }).locator('div').click();
    await page.getByPlaceholder('120').click();
    await page.getByPlaceholder('120').fill(rawMaterial.weight);
    await page.getByRole('button', { name: 'Add', exact: true }).click();
  }

  let ingredientCount = await page.$$eval(
    'div#add-ingredient-table table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(ingredientCount).toBe(CONST.testProduct.ingredients.length - 1);

  //wait for loading spinner to disappear
  await page.waitForFunction(
    () =>
      !document
        .querySelector('button#next-button')
        ?.classList.contains('ant-btn-loading')
  )

  await page.getByRole('button', { name: 'edit', exact: true }).first().click();

  await page.getByPlaceholder('Name').fill('mango');

  await page.click('button#search-ef-button');
  await page.getByRole('row', { name: 'mango production' }).first().getByLabel('', { exact: true }).check();
  await page.getByRole('button', { name: 'Update' }).click();
  await page.waitForSelector('text=mango production');

  //Packaging
  //wait for packaging predictions
  await page.click('button:has-text("Use Copilot")')

  await page.waitForSelector('text=PET', { timeout: 230000, state: 'visible' })

  const packagingCount = await page.$$eval(
    'div#add-packaging-table table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  //Edit Packaging
  await page.locator('#add-packaging-table')
    .locator('tr', { hasText: 'PET' })
    .getByRole('cell', { name: 'Edit Delete' })
    .getByRole('button', { name: 'Edit' })
    .click();

  await page.getByPlaceholder('90').click();
  await page.getByPlaceholder('90').fill('14.717');
  await page.getByRole('button', { name: 'Add', exact: true }).click();
  await page.waitForSelector('text=14.72 g');

  //Delete Packaging
  await await page.locator('#add-packaging-table')
    .locator('tr', { hasText: 'PET' })
    .getByRole('cell', { name: 'Edit Delete' })
    .getByRole('button', { name: 'Delete' })
    .click();

  await page.getByRole('button', { name: 'OK' }).click();


  for (const packaging of CONST.testProduct.packaging) {
    await page.waitForTimeout(1000);
    await page.getByRole('button', { name: 'plus-circle Add Packaging' }).click();
    await page.getByLabel('Packaging Material').click();
    await page.getByLabel('Packaging Material').fill(packaging.material);
    await page.getByTitle(packaging.material).locator('div').click();
    await page.click('input#packaging-component');
    await page.locator('input#packaging-component').fill(packaging.component);
    await page.getByTitle(packaging.component).locator('div').click();
    await page.getByPlaceholder('90').fill(packaging.weight);
    await page.getByRole('button', { name: 'Add', exact: true }).click();
  }

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Predicted', { timeout: 220000 });

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Factory to Retail', { timeout: 50000 });

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Consumer use', { timeout: 10000 });

  const consumerUseRowSelector =
  '#add-consumer-use-table table tbody tr:nth-child(2) td:nth-child(2)'
  // Wait until consumer use is predicted
  await page.waitForFunction(
    (consumerUseRowSelector) => {
      const cell = document.querySelector(consumerUseRowSelector)
      return cell && cell.textContent?.trim().includes('AI')
    },
    consumerUseRowSelector,
    { timeout: 120000 }
  )

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=End of life', { timeout: 1000 });

  await page.click('button:has-text("Next")')

  //wait for loading spinner to disappear
  await page.waitForFunction(
    () =>
      !document
        .querySelector('button#finish-button')
        ?.classList.contains('ant-btn-loading')
  )

  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(15000);

  //Submit
  await page.click('button:has-text("Save")')

  await page.context().storageState({ path: CONST.authFile })

  await page.waitForSelector(
    `text=Product ${CONST.testProduct.productName} created successfully`,
    { timeout: 520000 }
  )

  await page.context().storageState({ path: CONST.authFile })

  await page.screenshot({
    path: 'tests/screenshots/add_product_create_product.png',
  })

  await page.waitForURL(`${CONST.baseURL}/products`)

  await page.screenshot({
    path: 'tests/screenshots/add_product_inventory_redirect.png',
  })
})
