import { test, expect, Request } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.describe.serial('Product Emissions Tests', () => {
  let productEmissions = {
    rawMaterial: 0,
    manufacturing: 0,
    consumerUse: 0,
    endOfLife: 0,
  }

  test.beforeEach(() => {
    if (state.hasFailed) {
      test.skip(true, 'Skipping due to a previous test failure')
    }
  })

  test.afterEach(async ({ page }, testInfo) => {
    if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
      state.hasFailed = true;
    }
  })

  test.use({ storageState: CONST.authFile })

  const requestTimings = new Map<Request, number>();

  test('Edit Product', async ({ page }) => {
    test.setTimeout(480000)
    await page.goto(`${CONST.baseURL}/products`)
    await page.waitForSelector('text=Product Name')
    await page.context().storageState({ path: CONST.authFile })

    page.on('request', (request) => {
      requestTimings.set(request, Date.now());
    });

    page.on('console', msg => {
      console.log(`[Console ${msg.type()}] ${msg.text()}`);
    });

    page.on('response', async (response) => {

      console.log('Response URL:', response.url());
      console.log('Response Status:', response.status());

      const request = response.request();
      const startTime = requestTimings.get(request);
      if (startTime) {
        const latency = Date.now() - startTime;
        console.log(`Response Time: ${latency}ms`);
        requestTimings.delete(request);
      }

      if (response.url().includes('/graphql')) {
        try {
          const responseBody = await response.json();
          console.log('GraphQL Response:', JSON.stringify(responseBody));
        } catch (error) {
          console.error('Error parsing response:', error)
        }
      }
    })

    await page
      .locator('input#search-input')
      .pressSequentially('Vibrant Colors Laundry Plus', { delay: 100 })

    const productInfo = page
      .locator('table tr.ant-table-row.ant-table-row-level-0')
      .first()

    expect(productInfo).toBeVisible()

    await productInfo.locator('td:nth-child(3)').click()

    await page.waitForSelector(`text=Vibrant Colors Laundry Plus`)

    const rawMaterialTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .first()
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const rawMaterialEmissions = rawMaterialTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(rawMaterialEmissions))).toBe(false)

    productEmissions.rawMaterial = Number(rawMaterialEmissions)

    const manufacturingTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .nth(2)
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const manufacturingEmissions = manufacturingTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(manufacturingEmissions))).toBe(false)

    productEmissions.manufacturing = Number(manufacturingEmissions)

    const consumerUseTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .nth(4)
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const consumerUseEmissions = consumerUseTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(consumerUseEmissions))).toBe(false)

    productEmissions.consumerUse = Number(consumerUseEmissions)

    await page.screenshot({
      path: 'tests/screenshots/edit_product_lci_table_data.png',
    })

    const endOfLifeTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .nth(5)
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const endOfLifeEmissions = endOfLifeTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(endOfLifeEmissions))).toBe(false)

    productEmissions.endOfLife = Number(endOfLifeEmissions)

    await page.getByRole('tab', { name: 'Product Details' }).click()

    await page
      .getByRole('button', { name: 'right Product Details edit' })
      .getByRole('button')
      .click()

    await page.waitForSelector(`text=Edit Product`)

    await page.waitForSelector(
      `text=Let’s enter some basic information on your product`
    )

    await page
      .getByPlaceholder('Ariel Laundry Liquid')
      .fill('Vibrant Colors Laundry Plus 12')

    await page.getByPlaceholder('#').fill('982-3478919871212')

    await page.waitForTimeout(1000)

    await page.getByRole('button', { name: 'Materials' }).click()

    const ingredientCount = await page.$$eval(
      'div#add-ingredient-table table tr.ant-table-row.ant-table-row-level-0',
      (rows) => rows.length
    )

    expect(ingredientCount).toEqual(5)

    await page.locator('#add-packaging-table')
    .locator('tr', { hasText: 'PET' })
    .getByRole('cell', { name: 'Edit Delete' })
    .getByRole('button', { name: 'Edit' })
    .click();

    await page.getByLabel('Packaging Material').click();
    await page.getByLabel('Packaging Material').fill('Aluminum');
    await page.getByTitle('Aluminum').locator('div').click();
    await page.click('input#packaging-component');
    await page.locator('input#packaging-component').fill('Bottle');
    await page.getByTitle('Bottle').locator('div').click();

    await page.getByRole('button', { name: 'Add', exact: true }).click();

    //manufacturing
    await page.getByRole('button', { name: 'Manufacturing' }).click()
    await expect(page.locator('tbody')).toContainText('Total Carbon Emissions')
    await expect(page.locator('tbody')).toContainText('kg')

    await page.waitForTimeout(1000)

    //transport
    await page.getByRole('button', { name: 'Transportation' }).click()
    await expect(page.locator('tbody')).toContainText('Source to FactoryAI')
    await expect(page.locator('tbody')).toContainText(
      'United States (Average)'
    )
    await expect(page.locator('tbody')).toContainText('Factory to RetailAI')
    await expect(page.locator('tbody')).toContainText('United States (Average)')

    await page.waitForTimeout(1000)

    await page.getByRole('button', { name: 'Finish' }).click()

    //wait for loading spinner to disappear
    await page.waitForFunction(
      () =>
        !document
          .querySelector('button#finish-button')
          ?.classList.contains('ant-btn-loading')
    )

    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(15000)

    await page.click('button:has-text("Save")')

    await page.waitForSelector(
      `text=Product Vibrant Colors Laundry Plus 12 updated successfully`,
      { timeout: 420000 }
    )

    await page.waitForURL(`${CONST.baseURL}/products`)
  })

  test('Verify Edit Product', async ({ page }) => {
    test.setTimeout(420000)
    await page.goto(`${CONST.baseURL}/products`)
    await page.waitForSelector('text=Product Name')
    await page.context().storageState({ path: CONST.authFile })

    await page
      .locator('input#search-input')
      .pressSequentially('Vibrant Colors Laundry Plus 12', { delay: 100 })

    const productInfo = page
      .locator('table tr.ant-table-row.ant-table-row-level-0')
      .first()

    expect(productInfo).toBeVisible()

    await productInfo.locator('td:nth-child(3)').click()

    await page.waitForSelector(`text=Vibrant Colors Laundry Plus 12`)

    const rawMaterialTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .first()
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const rawMaterialEmissions = rawMaterialTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(rawMaterialEmissions))).toBe(false)

    const manufacturingTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .nth(2)
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const manufacturingEmissions = manufacturingTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(manufacturingEmissions))).toBe(false)

    const consumerUseTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .nth(4)
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const consumerUseEmissions = consumerUseTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(consumerUseEmissions))).toBe(false)

    const endOfLifeTableEmissions = await page
      .locator(
        'div#lifeCycleEmissionsTable table tr.ant-table-row.ant-table-row-level-0'
      )
      .nth(5)
      .locator('td')
      .nth(2)
      .locator('p')
      .innerText()

    const endOfLifeEmissions = endOfLifeTableEmissions
      .replace(' kg CO2e', '')
      .trim()

    expect(isNaN(Number(endOfLifeEmissions))).toBe(false)

    const updatedProductEmissions = {
      rawMaterial: Number(rawMaterialEmissions),
      manufacturing: Number(manufacturingEmissions),
      consumerUse: Number(consumerUseEmissions),
      endOfLife: Number(endOfLifeEmissions),
    }

    console.log('Product Emissions Before Edit:', productEmissions)
    console.log('Product Emissions After Edit:', updatedProductEmissions)

    //verify that the manufacturing, consumer use & eol emissions are the same before and after edit
    expect(productEmissions.manufacturing).toEqual(
      updatedProductEmissions.manufacturing
    )
    expect(productEmissions.consumerUse).toEqual(
      updatedProductEmissions.consumerUse
    )
    // expect(productEmissions.endOfLife).toEqual(
    //   updatedProductEmissions.endOfLife
    // )

    await page.getByRole('tab', { name: 'Product Details' }).click()

    await expect(page.locator('#product-info-card')).toContainText(
      '982-3478919871212'
    )

    await page
      .getByRole('button', { name: 'right Additional Information' })
      .click()

    await page.waitForSelector(`text=Test Attribute 12345`)

    await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click()

    //verify that the location is intact after edits
    const materialTable = await page.$$(
      'div#rawMaterialsIngredients table tr.ant-table-row.ant-table-row-level-0'
    )

    const firstRowMaterial = await materialTable[0].$$('td')
    const firstRowMaterialSourceLocation = await firstRowMaterial[6].innerText()

    expect(firstRowMaterialSourceLocation).toEqual('United States')

    await page.locator("#transportationTable .ant-collapse-expand-icon").click()

    await page.waitForTimeout(500)

    const transportationTable = await page.$$(
      'div#transportationTable table tr.ant-table-row.ant-table-row-level-0'
    )

    const firstRowTransportationMaterial = await transportationTable[0].$$('td')
    const firstRowTransportationMaterialOriginLocation = await firstRowTransportationMaterial[3].innerText()

    expect(firstRowTransportationMaterialOriginLocation).toEqual('United States')

    const firstRowTransportationMaterialDestinationLocation = await firstRowTransportationMaterial[4].innerText()

    expect(firstRowTransportationMaterialDestinationLocation).toEqual('Lake Zurich, United States')

    })
})