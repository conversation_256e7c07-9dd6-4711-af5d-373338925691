import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure');
  }
});

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
});

test.use({ storageState: CONST.authFile })


test('EF Matching', async ({ page }) => {
  test.setTimeout(120000)
  await page.goto(`${CONST.baseURL}/labs/ef-matching`)
  await page.waitForSelector('text=AI Emission Factor Matching Tool')
  await page.context().storageState({ path: CONST.authFile })

  page.on('response', async (response) => {
    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json();
        console.log('GraphQL Response:', JSON.stringify(responseBody));
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page
  .locator('input#search')
  .pressSequentially(
    "Citric Acid",
    { delay: 100 }
  )

  await page.click('button:has-text("Search")')

  await page.waitForSelector('text=citric acid production', { timeout: 120000 });

})