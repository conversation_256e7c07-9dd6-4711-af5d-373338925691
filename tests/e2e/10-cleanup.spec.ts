import { test, expect } from '@playwright/test'
import { CONST } from '../const'
const fs = require('fs')
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure');
  }
});

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
});

test.use({ storageState: CONST.authFile })

test('Delete Product', async ({ page }) => {
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible({ timeout: 1000 })

  const selectProductCheckbox = await productInfo.locator(
    'td:nth-child(1) input'
  )

  await selectProductCheckbox.check()

  await page.click('button#action-dropdown-button')
  await page.click('button#delete-product-button')
  await page.click('button#delete-product-modal-ok-button')
})

test('Verify Delete Product', async ({ page }) => {
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(productCount).toBe(0)
})