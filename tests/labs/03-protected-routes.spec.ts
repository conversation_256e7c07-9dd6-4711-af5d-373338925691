import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure');
  }
});

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
});

test.use({ storageState: CONST.authFile })

test('Test Redirects', async ({ page }) => {
  await page.goto(`${CONST.baseURL}/`)
  await page.context().storageState({ path: CONST.authFile })

  await page.waitForSelector('text=Welcome to Labs', { timeout: 10000 })

  await page.goto(`${CONST.baseURL}/products`)
  await page.context().storageState({ path: CONST.authFile })

  await page.waitForSelector('text=Welcome to Labs', { timeout: 10000 })

  await page.goto(`${CONST.baseURL}/product/add`)
  await page.context().storageState({ path: CONST.authFile })

  await page.waitForSelector('text=Welcome to Labs', { timeout: 10000 })

  await page.goto(`${CONST.baseURL}/suppliers`)
  await page.context().storageState({ path: CONST.authFile })

  await page.waitForSelector('text=Welcome to Labs', { timeout: 10000 })

  await page.goto(`${CONST.baseURL}/reports`)
  await page.context().storageState({ path: CONST.authFile })

  await page.waitForSelector('text=Welcome to Labs', { timeout: 10000 })

})
